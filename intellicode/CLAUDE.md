# CLAUDE.md

This file provides guidance to Claude Code (claude.ai/code) when working with code in this repository.

## Project Overview

IntelliCode 是一个基于 Go 的智能代码审查工具，使用 LLM（大语言模型）来分析代码变更并提供详细的代码审查反馈。通过 React Agent 架构实现自动化代码审查。

### 核心特性

- **CLI 模式运行**: 命令行界面，直接输出审查结果
- **React Agent**: 基于 Eino 框架的智能代码审查 Agent
- **多模型支持**: 支持 Claude、DeepSeek、Qwen 等多种 LLM 模型
- **可扩展架构**: 统一的 ToolWrapper 接口，支持工具插件化
- **事件驱动**: 支持工具执行状态和 LLM 响应的实时推送

## Architecture

### 系统架构图

```
                    ┌─────────────────┐
                    │   CLI Client    │
                    └─────────┬───────┘
                              │
                    ┌─────────▼─────────┐
                    │  IntelliCode Core │
                    │ ┌─────────────────┐│
                    │ │  Event System   ││
                    │ │┌───────────────┐││
                    │ ││ EventEmitter  │││
                    │ ││ (4 事件类型)   │││
                    │ │└───────────────┘││
                    │ └─────────────────┘│
                    │ ┌─────────────────┐│
                    │ │  Agent System   ││
                    │ │┌───────────────┐││
                    │ ││  DiffAgent    │││
                    │ ││ (React架构)   │││
                    │ │└───────────────┘││
                    │ └─────────────────┘│
                    │ ┌─────────────────┐│
                    │ │  Tool System    ││
                    │ │┌───────────────┐││
                    │ ││ ToolWrapper   │││
                    │ ││  统一接口     │││
                    │ │└───────────────┘││
                    │ └─────────────────┘│
                    └───────────────────┘
```

### 运行模式

- **CLI 模式**: 命令行模式，直接输出审查结果

### 核心组件

#### 应用程序入口

- **cmd/plugincli/main.go**: 主应用程序入口，支持 CLI 和服务器双模式运行

#### 事件系统 (Event System)

- **internal/pkg/event/**: EventEmitter 事件系统
  - `emitter.go` - EventEmitter 接口定义
  - `default_emitter.go` - 基于 Go channel 的默认实现
  - 支持四种事件类型：`toolstart`, `toolend`, `assistant`, `suggestions`

#### Agent 系统

- **internal/plugin/agent/**: DiffAgent 实现
  - 基于 React 架构的代码审查 Agent
  - 支持事件发送器注入，实现实时事件流
  - 集成多种 LLM 模型和工具链

#### 工具系统

- **internal/pkg/tool/**: 核心工具模块
  - `wrapper.go` - ToolWrapper 统一接口
  - `BaseTool` - 基础工具实现，提供事件发送功能
- **internal/plugin/tool/**: 插件工具实现
  - 代码审查、文件操作、grep 等具体工具

#### 基础设施

- **internal/pkg/config/**: 配置管理模块，支持开发和生产环境
- **internal/pkg/llm/**: LLM 管理器，支持多种模型（Claude、DeepSeek、Qwen）
- **internal/pkg/prompt/**: 提示模板管理，支持嵌入式和本地文件系统模板
- **internal/pkg/log/**: 结构化日志系统，基于 Zap

### 关键技术特性

#### 数据结构定义

使用 **protobuf** 进行数据结构定义：

- **protobuf**: 定义数据结构和事件类型（`pkg/proto/event/event.proto`）
- **类型安全**: 通过 protobuf 保证数据结构一致性
- **向后兼容**: 遵循 protobuf 的向后兼容性规则

#### 事件系统

基于 EventEmitter 的事件驱动架构：

- **事件驱动**: 支持工具执行状态和 LLM 响应的实时推送
- **四种标准事件类型**:
  - `toolstart`: 工具开始执行
  - `toolend`: 工具执行完成
  - `assistant`: LLM 流式响应
  - `suggestions`: 修复建议

#### EventEmitter 系统

- **线程安全**: 基于 Go channel 实现，支持并发访问
- **非阻塞发送**: 100 缓冲区，避免事件发送阻塞
- **自动清理**: 支持客户端断开检测和资源清理
- **双模式兼容**: NopEmitter 确保 CLI 模式正常工作

#### ToolWrapper 架构

- **统一接口**: 所有工具实现相同的 ToolWrapper 接口
- **自动事件包装**: `WrapInvokableRun` 自动处理 toolstart/toolend 事件
- **UI 显示控制**: `ShouldShowInUI()` 控制工具在界面中的显示
- **错误处理**: 统一的错误格式化和上报机制

### 依赖库

- **Eino Framework**: 用于 LLM 编排和 Agent 构建的核心框架
- **go-git**: Git 仓库操作
- **Viper**: 配置文件管理
- **Zap**: 结构化日志记录
- **Langfuse**: LLM 追踪和监控
- **protobuf**: 数据结构定义和序列化
- **protoc-gen-openapi**: 从 protobuf 生成 OpenAPI 规范

## Usage

### CLI 模式

```bash
# 使用开发环境配置
./build/bin/plugincli/plugincli-darwin-arm64 -repo-path /path/to/repo

# 使用生产环境配置
./build/bin/plugincli/plugincli-darwin-arm64 -repo-path /path/to/repo -env production

# 使用自定义配置文件
./build/bin/plugincli/plugincli-darwin-arm64 -repo-path /path/to/repo -config /path/to/config.toml
```


## Configuration

配置文件位于 `internal/pkg/config/configs/` 目录：

- `config.development.toml`: 开发环境配置
- `config.production.toml`: 生产环境配置

配置包含：

- LLM 模型配置（支持 Claude、DeepSeek、Qwen）
- Langfuse 追踪配置
- 日志配置

## Development

### 构建命令

```bash
# 构建当前平台版本
make build-plugincli

# 构建所有平台版本
make build-plugincli-all

# 构建特定平台版本
make build-plugincli-platform GOOS=linux GOARCH=amd64

# 清理构建产物
make build-clean
```

### 测试命令

```bash
# 运行单元测试
make unit-test

# 运行测试并生成覆盖率报告
make unit-test-coverage

# 生成 HTML 覆盖率报告
make unit-test-coverage-html

# 运行竞态检测
make unit-test-race

# 运行基准测试
make unit-test-bench
```

### 开发流程

- 需要使用 TDD 的开发方式，先针对需求和设计编写测试用例，当测试用例通过后，再根据测试用例开发功能
- 单元测试需要能体现出实际使用场景中的 use case
- 该项目使用 Go 1.23.3，依赖 Eino 框架进行 LLM 编排
- 如果需要运行 go build 验证代码，需要将构建目录指定为 `build/` 目录

### Go 最佳实践

- 使用 `github.com/pkg/errors` 包处理错误
- 使用 `any` 而不是 `interface{}`
- tools 如果需要返回错误信息给 AI，需要使用 `NewToolErrorf` 函数返回
- EventEmitter 在不使用时必须调用 `Close()` 方法清理资源
- ToolWrapper 实现时继承 BaseTool 可获得标准的事件发送功能

### 数据结构开发指南

- **protobuf 优先**: 新增数据结构优先在 `.proto` 文件中定义
- **向后兼容**: 遵循 protobuf 的向后兼容性规则
- **类型安全**: 利用 protobuf 生成的代码确保类型安全
