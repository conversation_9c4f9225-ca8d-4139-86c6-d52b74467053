#!/bin/bash

# Cross-platform build script for intellicode
# Usage: ./build.sh [command] [options]

set -e

# Configuration
PROJECT_NAME="intellicode"
SCRIPT_DIR="$(cd "$(dirname "$0")" && pwd)"
ROOT_DIR="$(dirname "$SCRIPT_DIR")"
BUILD_DIR="$ROOT_DIR/build"
BIN_DIR="$BUILD_DIR/bin"
APPS=("plugincli" "tui")
# APPS=("plugincli", "tui", "scan", "etc")

# Platform configurations
PLATFORMS=(
    "linux/amd64"
    "linux/arm64"
    "darwin/amd64"
    "darwin/arm64"
    "windows/amd64"
)

LINUX_PLATFORMS=("linux/amd64" "linux/arm64")
DARWIN_PLATFORMS=("darwin/amd64" "darwin/arm64")
WINDOWS_PLATFORMS=("windows/amd64")

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Build function for single app and platform
build_app() {
    local app="$1"
    local goos="$2"
    local goarch="$3"
    
    local output_dir="$BIN_DIR/$app"
    local binary_name="$app"
    
    if [ -n "$goos" ] && [ -n "$goarch" ]; then
        binary_name="$app-$goos-$goarch"
        if [ "$goos" = "windows" ]; then
            binary_name="$binary_name.exe"
        fi
    else
        # For current platform builds, use current OS and ARCH
        local current_goos="$(go env GOOS)"
        local current_goarch="$(go env GOARCH)"
        binary_name="$app-$current_goos-$current_goarch"
        if [ "$current_goos" = "windows" ]; then
            binary_name="$binary_name.exe"
        fi
    fi
    
    mkdir -p "$output_dir"
    
    local build_cmd="go build -o $output_dir/$binary_name ./cmd/$app"
    
    if [ -n "$goos" ] && [ -n "$goarch" ]; then
        log_info "Building $app for $goos/$goarch..."
        GOOS="$goos" GOARCH="$goarch" $build_cmd
    else
        log_info "Building $app for current platform..."
        $build_cmd
    fi
    
    # Convert absolute path to relative path from project root
    local full_path="$output_dir/$binary_name"
    local relative_path="${full_path#$ROOT_DIR/}"
    log_success "$app built successfully: $relative_path"
}

# Build for current platform
build_current() {
    local app="${1:-all}"
    
    if [ "$app" = "all" ]; then
        for app_name in "${APPS[@]}"; do
            build_app "$app_name"
        done
    else
        build_app "$app"
    fi
}

# Build for specific platform
build_platform() {
    local app="$1"
    local goos="$2"
    local goarch="$3"
    
    if [ -z "$goos" ] || [ -z "$goarch" ]; then
        log_error "GOOS and GOARCH must be specified"
        log_info "Usage: $0 platform <app> <GOOS> <GOARCH>"
        log_info "Example: $0 platform plugincli linux amd64"
        exit 1
    fi
    
    build_app "$app" "$goos" "$goarch"
}

# Build for multiple platforms
build_platforms() {
    local app="$1"
    shift
    local platforms=("$@")
    
    for platform in "${platforms[@]}"; do
        IFS='/' read -r goos goarch <<< "$platform"
        build_app "$app" "$goos" "$goarch"
    done
}

# Build for all platforms
build_all() {
    local app="${1:-all}"
    
    if [ "$app" = "all" ]; then
        for app_name in "${APPS[@]}"; do
            log_info "Building $app_name for all platforms..."
            build_platforms "$app_name" "${PLATFORMS[@]}"
        done
    else
        log_info "Building $app for all platforms..."
        build_platforms "$app" "${PLATFORMS[@]}"
    fi
    
    log_success "All platforms built successfully!"
}

# Build for Linux platforms
build_linux() {
    local app="${1:-all}"
    
    if [ "$app" = "all" ]; then
        for app_name in "${APPS[@]}"; do
            log_info "Building $app_name for Linux platforms..."
            build_platforms "$app_name" "${LINUX_PLATFORMS[@]}"
        done
    else
        log_info "Building $app for Linux platforms..."
        build_platforms "$app" "${LINUX_PLATFORMS[@]}"
    fi
    
    log_success "Linux platforms built successfully!"
}

# Build for macOS platforms
build_darwin() {
    local app="${1:-all}"
    
    if [ "$app" = "all" ]; then
        for app_name in "${APPS[@]}"; do
            log_info "Building $app_name for macOS platforms..."
            build_platforms "$app_name" "${DARWIN_PLATFORMS[@]}"
        done
    else
        log_info "Building $app for macOS platforms..."
        build_platforms "$app" "${DARWIN_PLATFORMS[@]}"
    fi
    
    log_success "macOS platforms built successfully!"
}

# Build for Windows platforms
build_windows() {
    local app="${1:-all}"
    
    if [ "$app" = "all" ]; then
        for app_name in "${APPS[@]}"; do
            log_info "Building $app_name for Windows platforms..."
            build_platforms "$app_name" "${WINDOWS_PLATFORMS[@]}"
        done
    else
        log_info "Building $app for Windows platforms..."
        build_platforms "$app" "${WINDOWS_PLATFORMS[@]}"
    fi
    
    log_success "Windows platforms built successfully!"
}

# Clean build artifacts
clean() {
    log_info "Cleaning build artifacts..."
    rm -rf "$BIN_DIR"
    log_success "Build artifacts cleaned!"
}

# Show help
show_help() {
    echo "Cross-platform build script for $PROJECT_NAME"
    echo ""
    echo "Usage: $0 <command> [options]"
    echo ""
    echo "Commands:"
    echo "  current [app]              Build for current platform (default: all apps)"
    echo "  platform <app> <os> <arch> Build for specific platform"
    echo "  all [app]                  Build for all platforms (default: all apps)"
    echo "  linux [app]               Build for Linux platforms (default: all apps)"
    echo "  darwin [app]              Build for macOS platforms (default: all apps)"
    echo "  windows [app]             Build for Windows platforms (default: all apps)"
    echo "  clean                      Clean build artifacts"
    echo "  help                       Show this help message"
    echo ""
    echo "Supported Apps: ${APPS[*]}"
    echo "Supported Platforms: ${PLATFORMS[*]}"
    echo ""
    echo "Examples:"
    echo "  $0 current                    # Build all apps for current platform"
    echo "  $0 current plugincli          # Build plugincli for current platform"
    echo "  $0 platform plugincli linux amd64  # Build plugincli for Linux amd64"
    echo "  $0 all                        # Build all apps for all platforms"
    echo "  $0 linux plugincli            # Build plugincli for Linux platforms"
    echo "  $0 clean                      # Clean build artifacts"
}

# Main function
main() {
    cd "$ROOT_DIR"
    
    case "${1:-help}" in
        "current")
            build_current "$2"
            ;;
        "platform")
            build_platform "$2" "$3" "$4"
            ;;
        "all")
            build_all "$2"
            ;;
        "linux")
            build_linux "$2"
            ;;
        "darwin")
            build_darwin "$2"
            ;;
        "windows")
            build_windows "$2"
            ;;
        "clean")
            clean
            ;;
        "help"|"--help"|"-h")
            show_help
            ;;
        *)
            log_error "Unknown command: $1"
            show_help
            exit 1
            ;;
    esac
}

# Run main function
main "$@"