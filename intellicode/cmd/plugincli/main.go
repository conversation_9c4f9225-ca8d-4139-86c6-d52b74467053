package main

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"intellicode/internal/pkg/config"
	eventemitter "intellicode/internal/pkg/emitter"
	"intellicode/internal/pkg/pragent"
	"intellicode/pkg/proto/event"
	"intellicode/internal/pkg/llm"
	"intellicode/internal/pkg/log"
	"intellicode/internal/pkg/prompt"
	"intellicode/internal/plugin/agent"
	"intellicode/internal/plugin/consts"
	httpserver "intellicode/internal/plugin/http"
	"intellicode/internal/plugin/schema"
	"intellicode/internal/plugin/utils"
	"os"
	"os/exec"
	"os/signal"
	"path/filepath"
	"slices"
	"strings"
	"syscall"
	"time"

	"github.com/cloudwego/eino/callbacks"
	"github.com/spf13/cobra"
	"go.uber.org/zap/zapcore"

	"github.com/cloudwego/eino-ext/callbacks/langfuse"
	"github.com/pkg/errors"
)

var (
	langfuseHandler callbacks.Handler
	flusher         func()
	llmManager      *llm.Manager
	appConfig       *config.Config
	templateManager prompt.TemplateManager
)

func init() {
	_ = log.Init(zapcore.DebugLevel, utils.GetLogDir(), consts.DefaultLogName)
	log.Infof("Intellicode is running")
}

// initializeApp 初始化应用配置和依赖
func initializeApp(configPath string, env config.Environment) error {
	ctx := context.Background()
	// Load configuration
	var err error
	if configPath != "" {
		// 使用指定的配置文件
		appConfig, err = config.LoadConfigFromFileSystem(configPath, env)
		if err != nil {
			return errors.Wrap(err, "failed to load config from file system")
		}
	} else {
		// 使用嵌入式配置
		appConfig, err = config.LoadConfigFromEmbed(env)
		if err != nil {
			return errors.Wrap(err, "failed to load config from embed")
		}
	}

	// 创建LLM管理器
	llmManager = llm.NewManager()

	// 初始化模型
	err = llmManager.InitModel(appConfig.ModelConfigs)
	if err != nil {
		return errors.Wrap(err, "failed to initialize LLM models")
	}

	// init PromptManager
	templateManager = prompt.NewTemplateManager()
	if err = templateManager.Load(ctx, env); err != nil {
		return errors.Wrap(err, "failed to initialize template manager")
	}

	// create langfuse handler
	langfuseHandler, flusher = langfuse.NewLangfuseHandler(&langfuse.Config{
		Host:      appConfig.LangfuseConfig.Host,
		PublicKey: appConfig.LangfuseConfig.PublicKey,
		SecretKey: appConfig.LangfuseConfig.SecretKey,
	})

	handlers := []callbacks.Handler{langfuseHandler}
	callbacks.AppendGlobalHandlers(handlers...)

	return nil
}

// parseAndValidateFiles 解析和验证文件列表
func parseAndValidateFiles(filesStr string, repoPath string) ([]string, error) {
	if filesStr == "" {
		return nil, nil
	}
	
	// 解析逗号分隔的文件列表
	files := strings.Split(filesStr, ",")
	var validFiles []string
	
	for _, file := range files {
		file = strings.TrimSpace(file)
		if file == "" {
			continue
		}
		
		// 转换为绝对路径进行检查
		var absPath string
		if filepath.IsAbs(file) {
			absPath = file
		} else {
			absPath = filepath.Join(repoPath, file)
		}
		
		// 检查文件是否存在
		if _, err := os.Stat(absPath); os.IsNotExist(err) {
			return nil, errors.Errorf("file not found: %s", file)
		}
		
		// 转换为相对于仓库根目录的路径
		relPath, err := filepath.Rel(repoPath, absPath)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to get relative path for %s", file)
		}
		
		// 确保文件在仓库目录内
		if strings.HasPrefix(relPath, "..") {
			return nil, errors.Errorf("file %s is outside repository", file)
		}
		
		validFiles = append(validFiles, relPath)
	}
	
	return validFiles, nil
}

// reviewCode 使用 React Agent 进行代码审查
func reviewCode(repoPath string, specifiedFiles []string) error {
	ctx := context.Background()
	
	// 创建事件发送器
	emitter := eventemitter.NewEmitter()
	defer emitter.Close()
	
	// 创建支持事件的 DiffAgent
	diffAgent, err := agent.NewDiffAgentWithEmitter(repoPath, llmManager, templateManager, 80, emitter)
	if err != nil {
		return errors.Wrap(err, "failed to create DiffAgent")
	}

	// 获取要审查的文件列表
	var reviewFiles []string
	
	if len(specifiedFiles) > 0 {
		// 使用用户指定的文件列表
		reviewFiles = specifiedFiles
	} else {
		// 获取仓库中有变更的已跟踪文件和未跟踪文件
		
		// 获取有变更的已跟踪文件
		cmdTracked := exec.Command("git", "diff", "--name-only")
		cmdTracked.Dir = repoPath
		outputTracked, err := cmdTracked.CombinedOutput()
		if err != nil {
			return errors.Wrap(err, "failed to get changed tracked files")
		}
		changedTrackedFiles := strings.Split(strings.TrimSpace(string(outputTracked)), "\n")
		for _, file := range changedTrackedFiles {
			if file != "" {
				reviewFiles = append(reviewFiles, file)
			}
		}

		// 获取暂存区的变更文件
		cmdStaged := exec.Command("git", "diff", "--name-only", "--staged")
		cmdStaged.Dir = repoPath
		outputStaged, err := cmdStaged.CombinedOutput()
		if err != nil {
			return errors.Wrap(err, "failed to get staged files")
		}
		stagedFiles := strings.Split(strings.TrimSpace(string(outputStaged)), "\n")
		for _, file := range stagedFiles {
			if file != "" {
				// 检查文件是否已经在列表中
				fileExists := false
				fileExists = slices.Contains(reviewFiles, file)
				if !fileExists {
					reviewFiles = append(reviewFiles, file)
				}
			}
		}

		// 获取未跟踪文件
		cmdUntracked := exec.Command("git", "ls-files", "--others", "--exclude-standard")
		cmdUntracked.Dir = repoPath
		outputUntracked, err := cmdUntracked.CombinedOutput()
		if err != nil {
			return errors.Wrap(err, "failed to get untracked files")
		}
		untrackedFiles := strings.Split(strings.TrimSpace(string(outputUntracked)), "\n")
		for _, file := range untrackedFiles {
			if file != "" {
				reviewFiles = append(reviewFiles, file)
			}
		}
	}

	if len(reviewFiles) == 0 {
		if len(specifiedFiles) > 0 {
			return errors.New("no files to review from specified files")
		}
		return errors.New("nothing to review, no changed files found")
	}

	// 异步执行代码审查
	done := make(chan error, 1)
	var suggestions []schema.Suggestion
	
	go func() {
		defer emitter.Close()
		err := diffAgent.Review(ctx, reviewFiles)
		if err != nil {
			done <- errors.Wrap(err, "failed to run code review")
			return
		}
		
		// 获取最终的建议结果（从 DiffAgent 中提取）
		suggestions = diffAgent.GetSuggestions()
		
		// 打印 result 详细信息
		fmt.Printf("\n=== Code Review Result ===\n")
		fmt.Printf("Total suggestions: %d\n", len(suggestions))
		for i, suggestion := range suggestions {
			fmt.Printf("--- Suggestion %d ---\n", i+1)
			fmt.Printf("ID: %d\n", suggestion.ID)
			fmt.Printf("Category: %s\n", suggestion.Category)
			fmt.Printf("Level: %s\n", suggestion.Level)
			fmt.Printf("File: %s\n", suggestion.FilePath)
			fmt.Printf("Lines: %d-%d\n", suggestion.StartLine, suggestion.EndLine)
			fmt.Printf("Description: %s\n", suggestion.Description)
			if suggestion.OldStr != "" {
				fmt.Printf("Old: %s\n", suggestion.OldStr)
			}
			if suggestion.NewStr != "" {
				fmt.Printf("New: %s\n", suggestion.NewStr)
			}
			fmt.Printf("-------------------------\n")
		}
		fmt.Printf("=========================\n\n")
		
		done <- nil
	}()

	// 主线程监听事件并实时打印
	for {
		select {
		case err := <-done:
			// 代码审查完成
			if err != nil {
				return err
			}
			// 打印最终的修复建议
			if len(suggestions) > 0 {
				fmt.Print(schema.FormatSuggestions(suggestions))
			}
			fmt.Println("✅ Code review completed successfully!")
			return nil
		case evt, ok := <-emitter.Events():
			if !ok {
				// 事件通道已关闭，等待完成
				continue
			}
			// 格式化并打印事件
			printEvent(evt)
		}
	}
}

// printEvent 格式化并打印事件
func printEvent(evt *event.Event) {
	switch evt.Type {
	case event.EventType_EVENT_TYPE_TOOL_START:
		if data := evt.GetToolStart(); data != nil {
			fmt.Printf("🔧 Calling Tool [%s] ...\n%s\n", data.Name, prettifyJSONString(data.Input))
		}
	case event.EventType_EVENT_TYPE_TOOL_END:
		if data := evt.GetToolEnd(); data != nil {
			if data.Status == event.ToolStatus_TOOL_STATUS_SUCCESS {
				fmt.Printf("🔧 Tool %s finished\n", data.Name)
			} else {
				fmt.Printf("❌ Tool %s failed: %s\n", data.Name, data.Output)
			}
		}
	case event.EventType_EVENT_TYPE_ASSISTANT:
		if data := evt.GetAssistant(); data != nil {
			fmt.Print(data.Chunk)
		}
	case event.EventType_EVENT_TYPE_SUGGESTIONS:
		if data := evt.GetSuggestions(); data != nil {
			// 将 event.Suggestion 转换为 schema.Suggestion
			schemaSuggestions := make([]schema.Suggestion, len(data.Suggestions))
			for i, s := range data.Suggestions {
				// 转换 Level 类型
				var level consts.Level
				switch s.Level {
				case "INFO", "info":
					level = consts.LevelInfo
				case "MINOR", "minor":
					level = consts.LevelMinor
				case "MAJOR", "major", "Major":
					level = consts.LevelMajor
				case "CRITICAL", "critical":
					level = consts.LevelCritical
				case "BLOCKER", "blocker":
					level = consts.LevelBlocker
				default:
					level = consts.LevelInfo
				}
				
				schemaSuggestions[i] = schema.Suggestion{
					ID:          int(s.Id),
					Category:    s.Category,
					Level:       level,
					FilePath:    s.FilePath,
					StartLine:   int(s.StartLine),
					EndLine:     int(s.EndLine),
					Description: s.Description,
					OldStr:      s.OldString,
					NewStr:      s.NewString,
				}
			}
			fmt.Print(schema.FormatSuggestions(schemaSuggestions))
		}
	}
}

// prettifyJSONString 格式化 JSON 字符串以便阅读
func prettifyJSONString(jsonStr string) string {
	indent := "  " // 默认2个空格

	var prettyJSON bytes.Buffer
	err := json.Indent(&prettyJSON, []byte(jsonStr), "", indent)
	if err != nil {
		return jsonStr
	}

	return prettyJSON.String()
}

// createReviewCommand 创建 review 子命令
func createReviewCommand() *cobra.Command {
	var (
		projectPath string
		files       []string
	)

	cmd := &cobra.Command{
		Use:   "review",
		Short: "Run code review on specified files",
		Long: `Run AI-powered code review on specified files or automatically detect changed files.
By default, it will analyze all changed files in the repository.`,
		Example: `  # Review all changed files
  intellicode review --project /path/to/project

  # Review specific files
  intellicode review --project /path/to/project --files src/main.go --files src/utils.go

  # Review multiple files at once
  intellicode review --project /path/to/project --files src/main.go,src/utils.go`,
		RunE: func(cmd *cobra.Command, args []string) error {
			// 验证必需参数
			if projectPath == "" {
				return errors.New("project is required")
			}

			// 解析和验证文件列表
			var specifiedFiles []string
			if len(files) > 0 {
				var err error
				specifiedFiles, err = parseAndValidateFilesFromSlice(files, projectPath)
				if err != nil {
					return errors.Wrap(err, "invalid files parameter")
				}
			}

			// 运行代码审查
			if err := reviewCode(projectPath, specifiedFiles); err != nil {
				return errors.Wrap(err, "code review failed")
			}

			// 清理资源
			if flusher != nil {
				flusher()
			}
			return nil
		},
	}

	// 添加参数
	cmd.Flags().StringVarP(&projectPath, "project", "p", "", "Project path for code review (required)")
	cmd.Flags().StringSliceVarP(&files, "files", "f", []string{}, "Files to review (supports multiple values and comma-separated)")

	// 标记必需参数
	cmd.MarkFlagRequired("project")

	// 设置文件路径补全
	cmd.MarkFlagFilename("project")
	cmd.MarkFlagFilename("files")

	return cmd
}


// parseAndValidateFilesFromSlice 解析和验证文件列表（从 slice 而不是逗号分隔字符串）
func parseAndValidateFilesFromSlice(filesSlice []string, repoPath string) ([]string, error) {
	var validFiles []string
	
	// 首先将 repoPath 转换为绝对路径
	absRepoPath, err := filepath.Abs(repoPath)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get absolute path for repository: %s", repoPath)
	}
	
	for _, fileStr := range filesSlice {
		// 支持逗号分隔的文件（向后兼容）
		files := strings.Split(fileStr, ",")
		for _, file := range files {
			file = strings.TrimSpace(file)
			if file == "" {
				continue
			}
			
			// 转换为绝对路径进行检查
			var absPath string
			if filepath.IsAbs(file) {
				absPath = file
			} else {
				// 相对路径基于当前工作目录，不是基于 repoPath
				absPath, err = filepath.Abs(file)
				if err != nil {
					return nil, errors.Wrapf(err, "failed to get absolute path for file: %s", file)
				}
			}
			
			// 检查文件是否存在
			if _, err := os.Stat(absPath); os.IsNotExist(err) {
				return nil, errors.Errorf("file not found: %s", file)
			}
			
			// 转换为相对于仓库根目录的路径
			relPath, err := filepath.Rel(absRepoPath, absPath)
			if err != nil {
				return nil, errors.Wrapf(err, "failed to get relative path for %s", file)
			}
			
			// 确保文件在仓库目录内
			if strings.HasPrefix(relPath, "..") {
				return nil, errors.Errorf("file %s is outside repository", file)
			}
			
			validFiles = append(validFiles, relPath)
		}
	}
	
	return validFiles, nil
}

// createServerCommand 创建 HTTP 服务器子命令
func createServerCommand() *cobra.Command {
	var (
		port           int
		pragentBaseURL string
		pragentToken   string
		pragentCICDToken string
	)

	cmd := &cobra.Command{
		Use:   "server",
		Short: "Start HTTP server for code review",
		Long: `Start HTTP server that provides REST API for code review.
The server supports SSE (Server-Sent Events) for real-time streaming of review progress.`,
		Example: `  # Start server on default port 8080
  intellicode server

  # Start server on custom port
  intellicode server --port 9090

  # Start server with pragent configuration
  intellicode server --port 8080 --pragent-url http://localhost:3000 --pragent-token your-token`,
		RunE: func(cmd *cobra.Command, args []string) error {
			// 创建 pragent 服务配置
			pragentConfig := &pragent.Config{
				BaseURL:    pragentBaseURL,
				Token:      pragentToken,
				CICDToken:  pragentCICDToken,
				Timeout:    30 * time.Second,
				RetryCount: 3,
			}

			// 创建 pragent 服务
			pragentService, err := pragent.NewRemoteService(pragentConfig)
			if err != nil {
				return errors.Wrap(err, "failed to create pragent service")
			}

			// 启动 HTTP 服务器
			return startHTTPServer(port, pragentService)
		},
	}

	// 添加参数
	cmd.Flags().IntVarP(&port, "port", "p", 8080, "HTTP server port")
	cmd.Flags().StringVar(&pragentBaseURL, "pragent-url", "", "Pragent service base URL")
	cmd.Flags().StringVar(&pragentToken, "pragent-token", "", "Pragent service token")
	cmd.Flags().StringVar(&pragentCICDToken, "pragent-cicd-token", "", "Pragent CICD token")

	return cmd
}

// startHTTPServer 启动 HTTP 服务器
func startHTTPServer(port int, pragentService *pragent.RemoteService) error {
	// 创建 HTTP 服务器配置
	serverConfig := &httpserver.ServerConfig{
		Port:            port,
		LLMManager:      llmManager,
		TemplateManager: templateManager,
		PragentService:  pragentService,
	}

	// 创建 HTTP 服务器
	server, err := httpserver.NewServer(serverConfig)
	if err != nil {
		return errors.Wrap(err, "failed to create HTTP server")
	}

	// 设置信号处理，用于优雅关闭
	// ctx, cancel := context.WithCancel(context.Background())
	// defer cancel()

	// 监听系统信号
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// 启动服务器
	errChan := make(chan error, 1)
	go func() {
		errChan <- server.Start()
	}()

	// 等待信号或错误
	select {
	case err := <-errChan:
		if err != nil {
			return errors.Wrap(err, "HTTP server failed")
		}
	case sig := <-sigChan:
		log.Infof("Received signal %v, shutting down server...", sig)
		
		// 优雅关闭
		shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer shutdownCancel()
		
		if err := server.Stop(shutdownCtx); err != nil {
			log.Errorf("Failed to shutdown server gracefully: %v", err)
			return err
		}
		
		log.Info("Server shutdown completed")
	}

	// 清理资源
	if flusher != nil {
		flusher()
	}

	return nil
}

func main() {
	// 创建根命令
	rootCmd := &cobra.Command{
		Use:   "intellicode",
		Short: "IntelliCode - AI-powered code review tool",
		Long: `IntelliCode is an intelligent code review tool that uses LLM to analyze code changes
and provide detailed feedback. It supports both CLI and HTTP server modes.`,
	}

	// 全局参数
	var (
		configPath string
		env        string
	)

	// 添加全局参数
	rootCmd.PersistentFlags().StringVarP(&configPath, "config", "c", "", "Path to config file (optional, defaults to embedded config)")
	rootCmd.PersistentFlags().StringVarP(&env, "env", "e", "development", "Environment (development, production, test)")

	// 添加子命令
	rootCmd.AddCommand(createReviewCommand())
	rootCmd.AddCommand(createServerCommand())
	
	// 禁用自动生成的 completion 命令
	rootCmd.CompletionOptions.DisableDefaultCmd = true

	// 设置全局的 PersistentPreRun 来初始化应用
	rootCmd.PersistentPreRun = func(cmd *cobra.Command, args []string) {
		// 验证环境参数
		envType := config.Environment(env)
		if envType != config.EnvDevelopment && envType != config.EnvProduction {
			fmt.Fprintf(os.Stderr, "Error: invalid environment '%s'. Valid values: development, production, test\n", env)
			os.Exit(1)
		}

		// 初始化应用配置
		if err := initializeApp(configPath, envType); err != nil {
			log.Fatalf("Failed to initialize app: %v", err)
		}
	}

	// 执行根命令
	if err := rootCmd.Execute(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}
