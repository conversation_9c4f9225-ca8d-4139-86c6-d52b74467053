package http

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"intellicode/internal/pkg/llm"
	"intellicode/internal/pkg/log"
	"intellicode/internal/pkg/pragent"
	"intellicode/internal/pkg/prompt"
	"intellicode/pkg/proto/api"
	"github.com/pkg/errors"
)

// Server HTTP 服务器
type Server struct {
	port           int
	reviewService  *ReviewService
	server         *http.Server
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Port            int
	LLMManager      *llm.Manager
	TemplateManager prompt.TemplateManager
	PragentService  *pragent.RemoteService
}

// NewServer 创建新的 HTTP 服务器
func NewServer(config *ServerConfig) (*Server, error) {
	if config.LLMManager == nil {
		return nil, errors.New("LLM manager is required")
	}
	if config.TemplateManager == nil {
		return nil, errors.New("template manager is required")
	}
	if config.PragentService == nil {
		return nil, errors.New("pragent service is required")
	}

	reviewService := NewReviewService(config.LLMManager, config.TemplateManager, config.PragentService)
	
	s := &Server{
		port:          config.Port,
		reviewService: reviewService,
	}

	// 设置路由
	mux := s.setupRoutes()
	
	s.server = &http.Server{
		Addr:         fmt.Sprintf(":%d", config.Port),
		Handler:      mux,
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Minute, // SSE 需要较长的写入超时时间
		IdleTimeout:  2 * time.Minute,
	}

	return s, nil
}

// setupRoutes 设置路由
func (s *Server) setupRoutes() http.Handler {
	mux := http.NewServeMux()
	
	// 代码审查 API
	mux.HandleFunc("/api/review", s.handleReview)
	
	// 健康检查
	mux.HandleFunc("/health", s.handleHealth)
	
	// 添加 CORS 中间件
	return s.corsMiddleware(mux)
}

// handleReview 处理代码审查请求
func (s *Server) handleReview(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// 解析请求体
	body, err := io.ReadAll(r.Body)
	if err != nil {
		http.Error(w, "Failed to read request body", http.StatusBadRequest)
		return
	}
	defer r.Body.Close()

	var reviewReq api.ReviewRequest
	if err := json.Unmarshal(body, &reviewReq); err != nil {
		http.Error(w, "Invalid request format", http.StatusBadRequest)
		return
	}

	// 基本验证
	if reviewReq.RepoPath == "" {
		http.Error(w, "Repository path is required", http.StatusBadRequest)
		return
	}

	log.Infof("Received review request for repo: %s, files: %v", 
		reviewReq.RepoPath, reviewReq.ReviewFiles)

	// 处理审查请求
	err = s.reviewService.StreamReview(w, &reviewReq)
	if err != nil {
		log.Errorf("Review request failed: %v", err)
		// SSE 连接可能已经建立，不能再写入标准 HTTP 错误
		return
	}
}

// handleHealth 健康检查
func (s *Server) handleHealth(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]string{
		"status": "healthy",
		"time":   time.Now().Format(time.RFC3339),
	})
}

// corsMiddleware CORS 中间件
func (s *Server) corsMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 设置 CORS 头
		w.Header().Set("Access-Control-Allow-Origin", "*")
		w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization, Cache-Control")
		w.Header().Set("Access-Control-Allow-Credentials", "true")

		// 处理预检请求
		if r.Method == http.MethodOptions {
			w.WriteHeader(http.StatusNoContent)
			return
		}

		next.ServeHTTP(w, r)
	})
}

// Start 启动服务器
func (s *Server) Start() error {
	log.Infof("Starting HTTP server on port %d", s.port)
	
	if err := s.server.ListenAndServe(); err != nil && !errors.Is(err, http.ErrServerClosed) {
		return errors.Wrapf(err, "failed to start HTTP server")
	}
	
	return nil
}

// Stop 停止服务器
func (s *Server) Stop(ctx context.Context) error {
	log.Info("Stopping HTTP server...")
	
	// 优雅关闭服务器
	if err := s.server.Shutdown(ctx); err != nil {
		return errors.Wrap(err, "failed to shutdown HTTP server")
	}
	
	// 事件发送器在每个请求中独立管理，这里不需要关闭
	
	log.Info("HTTP server stopped")
	return nil
}