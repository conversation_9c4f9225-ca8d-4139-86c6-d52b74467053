package http

import (
	"context"
	"fmt"
	"net/http"
	"strings"
	"time"

	"intellicode/internal/pkg/emitter"
	"intellicode/internal/pkg/llm"
	"intellicode/internal/pkg/log"
	"intellicode/internal/pkg/pragent"
	"intellicode/internal/pkg/prompt"
	"intellicode/internal/plugin/agent"
	"intellicode/pkg/proto/api"
	pb "intellicode/pkg/proto/event"

	"github.com/pkg/errors"
)

type ReviewService struct {
	llmManager      *llm.Manager
	templateManager prompt.TemplateManager
	pragent         *pragent.RemoteService
}

// NewReviewService 创建新的 ReviewService 实例
func NewReviewService(llmManager *llm.Manager, templateManager prompt.TemplateManager, pragentService *pragent.RemoteService) *ReviewService {
	return &ReviewService{
		llmManager:      llmManager,
		templateManager: templateManager,
		pragent:         pragentService,
	}
}

// StreamReview 处理代码审查请求，返回 SSE 流式响应
func (s *ReviewService) StreamReview(w http.ResponseWriter, req *api.ReviewRequest) error {
	// 设置 SSE 响应头
	s.setupSSEHeaders(w)

	// 创建上下文
	ctx := context.Background()

	// 为此请求创建专用的事件发送器
	eventEmitter := emitter.NewEmitter()
	defer eventEmitter.Close()

	// 为此请求创建专用的 DiffAgent
	diffAgent, err := agent.NewDiffAgentWithEmitter(req.RepoPath, s.llmManager, s.templateManager, 80, eventEmitter)
	if err != nil {
		s.writeSSEError(w, nil, "Failed to create review agent", err)
		return errors.Wrap(err, "failed to create DiffAgent")
	}

	// 在独立的 goroutine 中运行审查
	done := make(chan error, 1)
	go func() {
		defer close(done)
		err := diffAgent.Review(ctx, req.ReviewFiles)
		if err != nil {
			done <- err
			return
		}
		// Suggestions 数据现在通过事件系统自动发送，不需要手动处理
		done <- nil
	}()

	// 监听事件并转发为 SSE
	return s.streamEvents(w, eventEmitter, req, done)
}

// 删除这个方法，因为现在每个请求都创建独立的 emitter

// setupSSEHeaders 设置 SSE 响应头
func (s *ReviewService) setupSSEHeaders(w http.ResponseWriter) {
	w.Header().Set("Content-Type", "text/event-stream")
	w.Header().Set("Cache-Control", "no-cache")
	w.Header().Set("Connection", "keep-alive")
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Headers", "Cache-Control")
}

// streamEvents 监听事件并转发为 SSE 响应
func (s *ReviewService) streamEvents(w http.ResponseWriter, emitter emitter.EventEmitter, req *api.ReviewRequest, done <-chan error) error {
	flusher, ok := w.(http.Flusher)
	if !ok {
		return errors.New("streaming unsupported")
	}

	var reviewID int
	var sessionStarted bool

	for {
		select {
		case event := <-emitter.Events():
			if event == nil {
				continue
			}

			// 处理不同类型的事件
			switch event.Type {
			case pb.EventType_EVENT_TYPE_SESSION_START:
				if !sessionStarted {
					// 创建审查记录
					sessionData := event.GetSessionStart()
					if sessionData != nil {
						id, success := s.handleSessionStart(context.Background(), sessionData, req)
						if success {
							reviewID = id
						}
						sessionStarted = true
					}
				}
				s.writeSSEEvent(w, flusher, event)

			case pb.EventType_EVENT_TYPE_ASSISTANT:
				s.writeSSEEvent(w, flusher, event)

			case pb.EventType_EVENT_TYPE_TOOL_START, pb.EventType_EVENT_TYPE_TOOL_END:
				s.writeSSEEvent(w, flusher, event)

			case pb.EventType_EVENT_TYPE_SUGGESTIONS:
				// 持久化建议到远程服务
				if reviewID > 0 && event.GetSuggestions() != nil {
					err := s.handleSuggestions(context.Background(), reviewID, event.GetSuggestions().Suggestions)
					if err != nil {
						// 记录错误但继续处理，不发送 SSE 错误事件
						log.Errorf("Failed to persist suggestions for review %d: %v", reviewID, err)
					}
				}
				s.writeSSEEvent(w, flusher, event)

			case pb.EventType_EVENT_TYPE_SESSION_END:
				s.writeSSEEvent(w, flusher, event)
				// 会话结束，关闭连接
				return nil
			}

		case err := <-done:
			if err != nil {
				s.writeSSEError(w, flusher, "Review failed", err)
				return err
			}
			// 审查完成，等待 SESSION_END 事件

		case <-time.After(30 * time.Minute):
			// 超时处理
			s.writeSSEError(w, flusher, "Request timeout", errors.New("timeout after 30 minutes"))
			return errors.New("timeout")
		}
	}
}

// handleSessionStart 处理会话开始事件，创建审查记录
func (s *ReviewService) handleSessionStart(ctx context.Context, sessionData *pb.SessionStartData, req *api.ReviewRequest) (int, bool) {
	// 使用事件中的 session ID 作为 reviewMD5
	reviewMD5 := sessionData.SessionId

	// 创建审查记录
	pragentReq := &pragent.PrAgentReview{
		ReviewMD5: reviewMD5,
		RepoURL:   req.RepoPath,
		Files:     req.ReviewFiles,
		IDE:       s.getIDEString(req.Ide),
		LLMModel:  "claude-3.5-sonnet", // TODO: 从配置获取
	}

	resp, err := s.pragent.PostReview(ctx, pragentReq)
	if err != nil {
		// 记录错误但不返回 error，因为 SSE 事件应该正常发送
		log.Errorf("Failed to create review record for session %s: %v", sessionData.SessionId, err)
		return 0, false // 返回 false 表示创建失败，但不阻止后续处理
	}

	log.Infof("Created review record for session %s with ID %d", sessionData.SessionId, resp.Data.ReviewID)
	return resp.Data.ReviewID, true
}

// handleSuggestions 处理建议事件，持久化到远程服务
func (s *ReviewService) handleSuggestions(ctx context.Context, reviewID int, suggestions []*pb.Suggestion) error {
	if len(suggestions) == 0 {
		return nil
	}

	// 转换为 pragent 格式
	pragentSuggestions := make([]pragent.Suggestion, 0, len(suggestions))
	for _, s := range suggestions {
		pragentSuggestions = append(pragentSuggestions, pragent.Suggestion{
			FilePath:              s.FilePath,
			StartLineNumber:       int(s.StartLine),
			EndLineNumber:         int(s.EndLine),
			Level:                 s.Level,
			OriginalCodeSnippet:   s.OldString,
			SuggestedCodeSnippet:  s.NewString,
			SuggestionCategory:    s.Category,
			SuggestionDescription: s.Description,
		})
	}

	// 创建问题记录
	createReq := &pragent.PrAgentCreateIssuesRequest{
		ReviewID: reviewID,
		Issues:   pragentSuggestions,
	}

	_, err := s.pragent.CreateIssues(ctx, createReq)
	if err != nil {
		return errors.Wrap(err, "failed to create issues")
	}

	return nil
}

// convertSuggestionsToProto 方法已删除，现在 suggestions 通过事件系统自动发送

// generateReviewMD5 方法已删除，现在直接使用事件中的 session ID

// getIDEString 获取 IDE 字符串
func (s *ReviewService) getIDEString(ide api.IDE) string {
	switch ide {
	case api.IDE_IDE_JETBRAINS:
		return "jetbrains"
	case api.IDE_IDE_VSCODE:
		return "vscode"
	default:
		return "unknown"
	}
}

// writeSSEEvent 写入 SSE 事件
func (s *ReviewService) writeSSEEvent(w http.ResponseWriter, flusher http.Flusher, event *pb.Event) {
	// 将事件序列化为 JSON 或其他格式
	data := s.formatEventData(event)

	// 写入 SSE 格式
	fmt.Fprintf(w, "event: %s\n", s.getEventName(event.Type))
	fmt.Fprintf(w, "data: %s\n\n", data)
	flusher.Flush()
}

// writeSSEError 写入 SSE 错误
func (s *ReviewService) writeSSEError(w http.ResponseWriter, flusher http.Flusher, message string, err error) {
	errorData := fmt.Sprintf(`{"error": "%s", "details": "%s"}`, message, err.Error())
	fmt.Fprintf(w, "event: error\n")
	fmt.Fprintf(w, "data: %s\n\n", errorData)
	flusher.Flush()
}

// formatEventData 格式化事件数据
func (s *ReviewService) formatEventData(event *pb.Event) string {
	switch event.Type {
	case pb.EventType_EVENT_TYPE_SESSION_START:
		if data := event.GetSessionStart(); data != nil {
			return fmt.Sprintf(`{"sessionId": "%s", "files": %v}`, data.SessionId, data.ReviewFiles)
		}
	case pb.EventType_EVENT_TYPE_ASSISTANT:
		if data := event.GetAssistant(); data != nil {
			return fmt.Sprintf(`{"chunk": "%s"}`, strings.ReplaceAll(data.Chunk, "\"", "\\\""))
		}
	case pb.EventType_EVENT_TYPE_TOOL_START:
		if data := event.GetToolStart(); data != nil {
			return fmt.Sprintf(`{"name": "%s", "input": "%s", "status": "%s"}`,
				data.Name, data.Input, data.Status.String())
		}
	case pb.EventType_EVENT_TYPE_TOOL_END:
		if data := event.GetToolEnd(); data != nil {
			return fmt.Sprintf(`{"name": "%s", "output": "%s", "status": "%s"}`,
				data.Name, data.Output, data.Status.String())
		}
	case pb.EventType_EVENT_TYPE_SUGGESTIONS:
		if data := event.GetSuggestions(); data != nil {
			return fmt.Sprintf(`{"suggestions": %d}`, len(data.Suggestions))
		}
	case pb.EventType_EVENT_TYPE_SESSION_END:
		if data := event.GetSessionEnd(); data != nil {
			return fmt.Sprintf(`{"sessionId": "%s", "success": %t}`, data.SessionId, data.Success)
		}
	}
	return "{}"
}

// getEventName 获取事件名称
func (s *ReviewService) getEventName(eventType pb.EventType) string {
	switch eventType {
	case pb.EventType_EVENT_TYPE_SESSION_START:
		return "session_start"
	case pb.EventType_EVENT_TYPE_ASSISTANT:
		return "assistant"
	case pb.EventType_EVENT_TYPE_TOOL_START:
		return "tool_start"
	case pb.EventType_EVENT_TYPE_TOOL_END:
		return "tool_end"
	case pb.EventType_EVENT_TYPE_SUGGESTIONS:
		return "suggestions"
	case pb.EventType_EVENT_TYPE_SESSION_END:
		return "session_end"
	default:
		return "unknown"
	}
}
