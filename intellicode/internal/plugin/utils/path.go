package utils

import (
	"intellicode/internal/plugin/consts"
	"io"
	"io/ioutil"
	"os"
	"os/exec"
	"os/user"
	"path/filepath"
	"runtime"

	"github.com/pkg/errors"
)

func GetHomePath() string {
	if sudoUser := os.Getenv("SUDO_USER"); sudoUser != "" {
		if u, err := user.Lookup(sudoUser); err == nil {
			return u.HomeDir
		}
	} else {
		d, err := os.UserHomeDir()
		if err == nil {
			return d
		}
		u, err := user.Current()
		if err == nil {
			return u.HomeDir
		}
	}
	return ""
}

func IsSudoUser() bool {
	if sudoUser := os.Getenv("SUDO_USER"); sudoUser != "" {
		return true
	}
	return false
}

func GetIntelliCodeBinName() string {
	if IsWindows() {
		return "intellicode.exe"
	}
	return "intellicode"
}

func IsWindows() bool {
	return runtime.GOOS == "windows"
}

func GetIntelliCodePath() (string, error) {
	path, _ := filepath.Abs(os.Args[0])
	if _, err := os.Stat(path); err != nil {
		p, err := exec.LookPath(GetIntelliCodeBinName())
		return p, errors.Wrap(err, "")
	}
	return path, nil
}

func CopyFile(src, dst string) (err error) {
	in, err := os.Open(src)
	if err != nil {
		return
	}
	defer in.Close()

	out, err := os.Create(dst)
	if err != nil {
		return
	}
	defer func() {
		if e := out.Close(); e != nil {
			err = e
		}
	}()

	_, err = io.Copy(out, in)
	if err != nil {
		return
	}

	err = out.Sync()
	if err != nil {
		return
	}

	si, err := os.Stat(src)
	if err != nil {
		return
	}
	err = os.Chmod(dst, si.Mode())
	if err != nil {
		return
	}

	return
}

// CopyDir recursively copies a directory tree, attempting to preserve permissions.
// Source directory must exist
// Symlinks are ignored and skipped.
func CopyDir(src string, dst string) (err error) {
	src = filepath.Clean(src)
	dst = filepath.Clean(dst)

	si, err := os.Stat(src)
	if err != nil {
		return err
	}
	if !si.IsDir() {
		return errors.New("source is not a directory")
	}

	_, err = os.Stat(dst)
	if err != nil && !os.IsNotExist(err) {
		return
	}

	err = os.MkdirAll(dst, si.Mode())
	if err != nil {
		return
	}

	entries, err := ioutil.ReadDir(src)
	if err != nil {
		return
	}

	for _, entry := range entries {
		srcPath := filepath.Join(src, entry.Name())
		dstPath := filepath.Join(dst, entry.Name())

		if entry.IsDir() {
			err = CopyDir(srcPath, dstPath)
			if err != nil {
				return
			}
		} else {
			// Skip symlinks.
			if entry.Mode()&os.ModeSymlink != 0 {
				continue
			}

			err = CopyFile(srcPath, dstPath)
			if err != nil {
				return
			}
		}
	}

	return
}

func GetIntelliCodeHomeDir() string {
	return filepath.Join(GetHomePath(), consts.DefaultIntelliCodeHomeDirName)
}

func GetLogDir() string {
	return filepath.Join(GetIntelliCodeHomeDir(), consts.DefaultLogDirName)
}

func GetRipGrepDir() string {
	return filepath.Join(GetIntelliCodeHomeDir(), consts.DefaultBinDirName, "ripgrep")
}

func GetTodoDir() string {
	return filepath.Join(GetIntelliCodeHomeDir(), consts.DefaultTodoDirName)
}

func GetProjectsDir() string {
	return filepath.Join(GetIntelliCodeHomeDir(), consts.DefaultProjectDirName)
}
