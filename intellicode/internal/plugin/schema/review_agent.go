package schema

import (
	"fmt"
	"intellicode/internal/pkg/code"
	"intellicode/internal/plugin/consts"
	"strings"
)

// ProjectInfo 包含项目基本信息
type ProjectInfo struct {
	ProjectDirectory string `json:"project_directory"`
}

// GroupCodeChange 表示一组相关的代码变更
type GroupCodeChange struct {
	Name          string           `json:"name"`
	Functionality string           `json:"functionality"`
	ReviewPoints  []string         `json:"reviewPoints"`
	Patches       []code.CodePatch `json:"patches"`
	Files         []string         `json:"files"`
}

// SummaryGroup 表示代码变更的摘要分组
type SummaryGroup struct {
	Name          string   `json:"name"`
	Functionality string   `json:"functionality"`
	ReviewPoints  []string `json:"reviewPoints"`
	Files         []string `json:"files"`
}

// SummaryResult 包含所有摘要分组的结果
type SummaryResult struct {
	Groups []SummaryGroup `json:"groups"`
}

type Suggestion struct {
	ID          int          `json:"id"`       // 唯一标识符，自增
	Category    string       `json:"category"` // code_defect, refactor
	Level       consts.Level `json:"level"`
	OldStr      string       `json:"old_string"` // from IssueFixer
	NewStr      string       `json:"new_string"` // from IssueFixer
	FilePath    string       `json:"file_path"`
	StartLine   int          `json:"start_line"`
	EndLine     int          `json:"end_line"`
	Description string       `json:"description"`
}

// String 格式化单个 Suggestion 的显示
func (s Suggestion) String() string {
	var result strings.Builder
	result.WriteString(fmt.Sprintf("【%s】%s\n", s.Level, s.Category))
	result.WriteString(fmt.Sprintf("   文件: %s:%d-%d\n", s.FilePath, s.StartLine, s.EndLine))
	result.WriteString(fmt.Sprintf("   描述: %s\n", s.Description))
	if s.OldStr != "" && s.NewStr != "" {
		result.WriteString("   修复方案:\n")
		result.WriteString(fmt.Sprintf("     原代码:\n%s\n", IndentCodeSnippet(s.OldStr)))
		result.WriteString(fmt.Sprintf("     修复后:\n%s\n", IndentCodeSnippet(s.NewStr)))
	}
	return result.String()
}

// FormatSuggestions 格式化 Suggestion 数组的显示
func FormatSuggestions(suggestions []Suggestion) string {
	if len(suggestions) == 0 {
		return "💡 修复建议: 无建议\n"
	}

	var result strings.Builder
	result.WriteString(fmt.Sprintf("💡 修复建议 (共 %d 条):\n", len(suggestions)))
	for i, suggestion := range suggestions {
		result.WriteString(fmt.Sprintf("\n%d. %s", i+1, suggestion.String()))
	}
	return result.String()
}

// IndentCodeSnippet 对代码片段进行缩进处理
func IndentCodeSnippet(snippet string) string {
	lines := strings.Split(snippet, "\n")
	var indentedLines []string
	for _, line := range lines {
		indentedLines = append(indentedLines, "     "+line)
	}
	return strings.Join(indentedLines, "\n")
}
