package agent

import (
	"context"
	"encoding/json"
	"fmt"
	baseagent "intellicode/internal/pkg/agent"
	"intellicode/internal/pkg/code"
	"intellicode/internal/pkg/code/repo"
	"intellicode/internal/pkg/config"
	"intellicode/internal/pkg/emitter"
	"intellicode/internal/pkg/llm"
	"intellicode/internal/pkg/log"
	"intellicode/internal/pkg/prompt"
	localtool "intellicode/internal/pkg/tool"
	"intellicode/internal/plugin/schema"
	pluginTool "intellicode/internal/plugin/tool"
	pluginutils "intellicode/internal/plugin/utils"
	"io"
	"path/filepath"
	"strings"

	"github.com/cloudwego/eino-ext/callbacks/langfuse"
	"github.com/cloudwego/eino/callbacks"
	"github.com/cloudwego/eino/components/model"
	template "github.com/cloudwego/eino/components/prompt"
	einotool "github.com/cloudwego/eino/components/tool"
	"github.com/cloudwego/eino/compose"
	"github.com/cloudwego/eino/flow/agent"
	"github.com/cloudwego/eino/flow/agent/react"
	einoschema "github.com/cloudwego/eino/schema"
	callbackutils "github.com/cloudwego/eino/utils/callbacks"
	"github.com/google/uuid"
	"github.com/pkg/errors"
)

type DiffAgent struct {
	baseagent.BaseAgent // 内嵌 BaseAgent 以获得 todo 管理器功能

	gitRepo         *repo.GitRepo
	projectDir      string
	issueWrapper    *pluginTool.IssueRecordWrapper // 新增: IssueRecordWrapper 用于拦截和记录 Issue 操作
	issueFixWrapper *pluginTool.IssueFixWrapper    // 新增: IssueFixWrapper 用于拦截和记录 IssueFix 操作
	eventEmitter    emitter.EventEmitter             // 新增: 事件发送器
}

// NewDiffAgentWithEmitter 创建支持事件发送器的 DiffAgent
func NewDiffAgentWithEmitter(repoPath string, llmManager *llm.Manager, template prompt.TemplateManager, maxStep int, emitter emitter.EventEmitter) (*DiffAgent, error) {
	gr, err := repo.NewGitRepo(repoPath)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to create git repo")
	}

	// 将 repoPath 转换为绝对路径
	absPath, err := filepath.Abs(repoPath)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get absolute path for repo")
	}

	agentModel, err := llmManager.GetModel(config.Claude37Sonnet)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get agent model")
	}
	chatModel, err := llmManager.GetModel(config.Claude37Sonnet)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get chat model")
	}

	if emitter == nil {
		return nil, errors.New("must pass emitter")
	}

	da := &DiffAgent{
		BaseAgent: baseagent.BaseAgent{
			LlmManager: llmManager,
			Template:   template,
			MaxStep:    maxStep,
			UUID:       uuid.New().String(),
			AgentModel: agentModel,
			FastModel:  chatModel,
		},
		gitRepo:      gr,
		projectDir:   absPath,
		eventEmitter: emitter,
	}

	// 初始化 IssueRecordWrapper
	da.issueWrapper = pluginTool.NewIssueRecordWrapper()

	// 初始化 IssueFixWrapper
	originalIssueFixTool := pluginTool.NewIssueFixTool()
	da.issueFixWrapper = pluginTool.NewIssueFixWrapper(originalIssueFixTool, da.issueWrapper)

	// 初始化 todo 管理器
	if err := da.InitializeTodoManager(pluginutils.GetTodoDir()); err != nil {
		return nil, errors.Wrap(err, "failed to initialize todo manager")
	}

	return da, nil
}

// GetIssueRecords 获取所有问题记录
func (da *DiffAgent) GetIssueRecords() []pluginTool.IssueRecordItem {
	if da.issueWrapper == nil {
		return nil
	}
	return da.issueWrapper.GetRecords()
}

// GetSuggestions 获取所有修复建议
func (da *DiffAgent) GetSuggestions() []schema.Suggestion {
	if da.issueFixWrapper == nil {
		return nil
	}
	return da.issueFixWrapper.GetSuggestions()
}

// GetEventEmitter 获取事件发送器
func (da *DiffAgent) GetEventEmitter() emitter.EventEmitter {
	return da.eventEmitter
}

func (da *DiffAgent) Review(ctx context.Context, reviewFiles []string) error {
	// 获取代码变更
	codechange, err := da.gitRepo.GetCodeChange(reviewFiles)
	if err != nil {
		return errors.Wrapf(err, "failed to get repo diff")
	}
	log.Debugf("start review with files: %s", codechange.AllFilesPath())

	// summary, err := da.summary(ctx, codechange)
	// if err != nil {
	// 	return errors.Wrapf(err, "failed to get summary")
	// }
	//
	// return da.react(ctx, summary, codechange)

	err = da.review(ctx, codechange)
	if err != nil {
		return err
	}

	return nil
}

func (da *DiffAgent) summary(ctx context.Context, codechange code.CodeChange) (schema.SummaryResult, error) {
	messages, err := da.buildSummaryMessages(codechange)
	if err != nil {
		return schema.SummaryResult{}, errors.Wrapf(err, "failed to build messages")
	}

	// 使用 LLM 进行摘要生成
	var buffer strings.Builder
	sr, err := da.FastModel.Stream(ctx, messages)
	if err != nil {
		return schema.SummaryResult{}, errors.Wrapf(err, "failed to summarize code change")
	}
	defer sr.Close()

	for {
		chunk, err := sr.Recv()
		if err != nil {
			if errors.Is(err, io.EOF) {
				break
			}
			return schema.SummaryResult{}, errors.Wrapf(err, "failed to receive stream chunk")
		}

		buffer.WriteString(chunk.Content)
		fmt.Print(chunk.Content)

	}

	summaryResp := buffer.String()
	jsonStr, err := llm.ExtractJsonFromString(summaryResp)
	if err != nil {
		return schema.SummaryResult{}, errors.Wrapf(err, "failed to extract json from summary response")
	}

	var summaryResult schema.SummaryResult
	if err := json.Unmarshal([]byte(jsonStr), &summaryResult); err != nil {
		return schema.SummaryResult{}, errors.Wrapf(err, "failed to unmarshal summary result")
	}

	return summaryResult, nil
}

func (da *DiffAgent) review(ctx context.Context, codechange code.CodeChange) error {
	tools := da.createTools()

	rAgent, err := react.NewAgent(ctx, &react.AgentConfig{
		ToolCallingModel: da.AgentModel,
		ToolsConfig: compose.ToolsNodeConfig{
			Tools: tools,
		},
		MaxStep:               da.MaxStep,
		StreamToolCallChecker: llm.StreamingToolCallChecker,
	})
	if err != nil {
		return errors.Wrapf(err, "failed to create react agent")
	}
	if rAgent == nil {
		log.Errorf("create React Agent got nil: %s", err)
	}

	messages, err := da.buildReviewMessages(ctx, codechange)
	if err != nil {
		return errors.Wrapf(err, "fieled to build messages")
	}

	// 创建回调处理器用于记录 LLM 请求和响应
	llmHandler := &llmCallback{emitter: da.eventEmitter}
	toolHandler := &toolCallback{}
	callbacks := react.BuildAgentCallback(
		&callbackutils.ModelCallbackHandler{
			OnStart:               llmHandler.OnStart,
			OnEnd:                 llmHandler.OnEnd,
			OnEndWithStreamOutput: llmHandler.OnEndWithStreamOutput,
			OnError:               llmHandler.OnError,
		},
		&callbackutils.ToolCallbackHandler{
			OnStart:               toolHandler.OnStart,
			OnEnd:                 toolHandler.OnEnd,
			OnEndWithStreamOutput: toolHandler.OnEndWithStreamOutput,
			OnError:               toolHandler.OnError,
		},
	)

	// langfuse trace
	toolmetadata := make(map[string]string)
	for _, tool := range tools {
		toolInfo, _ := tool.Info(ctx)
		openAPIInfo := localtool.ToolInfo2OpenAPI(toolInfo)
		info, _ := json.Marshal(openAPIInfo)
		toolmetadata[toolInfo.Name] = string(info)
	}
	traceId := uuid.New().String()
	ctx = langfuse.SetTrace(
		ctx,
		langfuse.WithName("ReviewAgent"),
		langfuse.WithID(traceId),
		langfuse.WithMetadata(toolmetadata),
	)
	// TODO: Set reviewFiles data as langfuse trace input

	// 发送会话开始事件
	if da.eventEmitter != nil {
		da.eventEmitter.EmitSessionStart(ctx, traceId, codechange.AllFilesPath())
	}

	// 确保在方法结束时发送会话结束事件
	defer func() {
		if da.eventEmitter != nil {
			da.eventEmitter.EmitSessionEnd(ctx, traceId, err == nil)
		}
	}()

	log.Infof("Agent starting with traceId %s...", traceId)

	sr, err := rAgent.Stream(ctx, messages, agent.WithComposeOptions(compose.WithCallbacks(callbacks)))
	if err != nil {
		return errors.Wrapf(err, "failed to run react agent")
	}

	defer sr.Close()

	log.Infof("Agent start streaming...")

	for {
		chunk, err := sr.Recv()
		if err != nil {
			if errors.Is(err, io.EOF) {
				break
			}
			return errors.Wrapf(err, "failed to receive stream chunk")
		}

		if da.eventEmitter != nil {
			da.eventEmitter.EmitAssistant(ctx, chunk.Content)
		}
	}

	log.Info("ReviewAgent END")

	return nil
}

// createTools 创建并注册工具
func (da *DiffAgent) createTools() []einotool.BaseTool {
	// 创建所有工具的 Wrapper 实例
	tools := []localtool.ToolWrapper{
		// 文件操作工具
		localtool.NewReadToolWrapper(da.projectDir),
		localtool.NewReadMultipleFilesWrapper(da.projectDir),
		localtool.NewLSTreeWrapper(da.projectDir),

		// 搜索工具
		localtool.NewRipGrepWrapper(da.projectDir),

		// 任务管理工具
		localtool.NewTodoWriteWrapper(da.GetTodoManager()),
		localtool.NewTodoReadWrapper(da.GetTodoManager()), // 不显示在 UI

		// 代码审查工具
		pluginTool.NewIssueAnalyzerWrapper(),
		pluginTool.NewIssueRecordWrapper(),
		pluginTool.NewIssueFixWrapperNew(),
	}

	// 为所有工具注入事件发送器
	wrappedTools := make([]einotool.BaseTool, 0, len(tools))
	for _, tool := range tools {
		tool.SetEventEmitter(da.eventEmitter)

		// 应用错误处理包装器
		wrappedTool := localtool.WrapInvokableToolWithErrorHandler(tool, localtool.ToolErrorHandler)
		wrappedTools = append(wrappedTools, wrappedTool)
	}

	return wrappedTools
}

func (da *DiffAgent) buildSummaryMessages(codechange code.CodeChange) ([]*einoschema.Message, error) {
	ctx := context.Background()
	summary, err := da.Template.Get(ctx, "review_summary.prompt")
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get agent template")
	}

	userPrompt, err := da.Template.Get(ctx, "code_change.prompt")
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get code_change template")
	}

	temp := template.FromMessages(
		einoschema.Jinja2,
		einoschema.SystemMessage(summary),
		einoschema.UserMessage(userPrompt),
	)
	messages, err := temp.Format(ctx, map[string]any{
		"code_change": codechange.Files,
	})
	if err != nil {
		return nil, errors.Wrap(err, "failed to format messages")
	}
	return messages, nil
}

func (da *DiffAgent) genGroupedCodeChanges(summary schema.SummaryResult, codechange code.CodeChange) []schema.GroupCodeChange {
	// 按 summary.Groups 分组构建 groupedCodeChanges
	groupedCodeChanges := make([]schema.GroupCodeChange, 0)
	if len(summary.Groups) > 0 {
		for _, g := range summary.Groups {
			patchs := make([]code.CodePatch, 0)
			for _, file := range g.Files {
				for _, cp := range codechange.Files {
					if cp.Path == file {
						patchs = append(patchs, cp)
					}
				}
			}
			groupedCodeChanges = append(groupedCodeChanges, schema.GroupCodeChange{
				Name:          g.Name,
				Functionality: g.Functionality,
				ReviewPoints:  g.ReviewPoints,
				Patches:       patchs,
				Files:         g.Files,
			})
		}
	}
	return groupedCodeChanges
}

func (da *DiffAgent) buildReviewMessages(ctx context.Context, codechange code.CodeChange) ([]*einoschema.Message, error) {
	agentSystem, err := da.Template.Get(ctx, "review_agent.prompt")
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get agent template")
	}

	projectInfo := schema.ProjectInfo{
		ProjectDirectory: da.projectDir,
	}
	codeChangePrompt, err := da.Template.Get(ctx, "code_change.prompt")
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get code_change template")
	}

	temp := template.FromMessages(
		einoschema.Jinja2,
		einoschema.SystemMessage(agentSystem),
		einoschema.UserMessage(codeChangePrompt),
	)

	git_status, err := da.gitRepo.GetStatus()
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get GitStatus from repo")
	}

	messages, err := temp.Format(ctx, map[string]any{
		"project_info":     projectInfo,
		"git_status":       git_status,
		"code_change":      codechange.Files,
		"user_instruction": "", // TODO: 用户定制
	})
	if err != nil {
		return nil, errors.Wrap(err, "failed to format messages")
	}
	return messages, nil
}

// buildMessages 构建LLM 消息列表
func (da *DiffAgent) buildMessages(summary schema.SummaryResult, codechange code.CodeChange) ([]*einoschema.Message, error) {
	ctx := context.Background()
	agentSystem, err := da.Template.Get(ctx, "review_agent.prompt")
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get agent template")
	}

	projectInfo := schema.ProjectInfo{
		ProjectDirectory: da.projectDir,
	}
	codeChangePrompt, err := da.Template.Get(ctx, "code_change.prompt")
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get code_change template")
	}

	groupedCodeChanges := da.genGroupedCodeChanges(summary, codechange)

	temp := template.FromMessages(
		einoschema.Jinja2,
		einoschema.SystemMessage(agentSystem),
		einoschema.UserMessage(codeChangePrompt),
	)

	messages, err := temp.Format(ctx, map[string]any{
		"project_info":     projectInfo,
		"file_content":     codechange.AllFilesPath(),
		"grouped_code":     groupedCodeChanges,
		"user_instruction": "", // TODO: 用户定制
	})
	if err != nil {
		return nil, errors.Wrap(err, "failed to format messages")
	}
	return messages, nil
}

type llmCallback struct {
	callbackutils.ModelCallbackHandler
	emitter emitter.EventEmitter
}

func (lc *llmCallback) OnStart(ctx context.Context, runInfo *callbacks.RunInfo, input *model.CallbackInput) context.Context {
	// log.Debugf("[LLM Callback] OnStart - RunInfo: Name=%s, Type=%s, Component=%v", runInfo.Name, runInfo.Type, runInfo.Component)
	// var tools []byte
	// if len(input.Tools) > 0 {
	// 	tools, _ = json.Marshal(input.Tools)
	// } else {
	// 	tools = []byte("[]")
	// }
	// log.Debugf("[LLM Callback] OnStart - Input: Tools=%s", tools)
	// log.Debugf("[LLM Callback] OnStart - Input: Extra=%+v", input.Extra)
	// log.Debugf("[LLM Callback] OnStart - Input: Config=%+v", input.Config)
	// log.Debugf("[LLM Callback] OnStart - Input: Messages=%+v", input.Messages)

	return ctx
}

func (lc *llmCallback) OnEnd(ctx context.Context, runInfo *callbacks.RunInfo, output *model.CallbackOutput) context.Context {
	// log.Debugf("[LLM Callback] OnEnd - RunInfo: Name=%s, Type=%s, Component=%v", runInfo.Name, runInfo.Type, runInfo.Component)
	// log.Debugf("[LLM Callback] OnEnd - Output: Message=%+v, Usage=%+v", output.Message, output.TokenUsage)
	// log.Debugf("[LLM Callback] OnEnd - Output: Extra=%+v", output.Extra)

	return ctx
}

func (lc *llmCallback) OnEndWithStreamOutput(ctx context.Context, runInfo *callbacks.RunInfo, output *einoschema.StreamReader[*model.CallbackOutput]) context.Context {
	// log.Debugf("[LLM Callback] OnEndWithStreamOutput - RunInfo: Name=%s, Type=%s, Component=%v", runInfo.Name, runInfo.Type, runInfo.Component)
	// log.Debugf("[LLM Callback] OnEndWithStreamOutput - StreamOutput received")

	for {
		chunk, err := output.Recv()
		if err != nil {
			if errors.Is(err, io.EOF) {
				break
			}
			log.Errorf("[LLM Callback] OnEndWithStreamOutput - Error: %v", err)
			break
		}

		if len(chunk.Message.Content) > 0 {
			// 通过事件发送流式内容
			if lc.emitter != nil {
				lc.emitter.EmitAssistant(ctx, chunk.Message.Content)
			}
		}
	}

	return ctx
}

func (lc *llmCallback) OnError(ctx context.Context, runInfo *callbacks.RunInfo, err error) context.Context {
	log.Errorf("[LLM Callback] OnError - RunInfo: Name=%s, Type=%s, Component=%v", runInfo.Name, runInfo.Type, runInfo.Component)
	log.Errorf("[LLM Callback] OnError - Error: %v", err)
	return ctx
}

type toolCallback struct {
	callbackutils.ToolCallbackHandler
}

func (tc *toolCallback) OnStart(ctx context.Context, runInfo *callbacks.RunInfo, input *einotool.CallbackInput) context.Context {
	// log.Debugf("[Tool Callback] OnStart - RunInfo: Name=%s, Type=%s, Component=%v", runInfo.Name, runInfo.Type, runInfo.Component)
	// log.Debugf("[Tool Callback] OnStart - Input: ArgumentsInJSON=%s, Extra=%+v", input.ArgumentsInJSON, input.Extra)

	return ctx
}

func (tc *toolCallback) OnEnd(ctx context.Context, runInfo *callbacks.RunInfo, output *einotool.CallbackOutput) context.Context {
	// log.Debugf("[Tool Callback] OnEnd - RunInfo: Name=%s, Type=%s, Component=%v", runInfo.Name, runInfo.Type, runInfo.Component)
	// log.Debugf("[Tool Callback] OnEnd - Output: Response with length=%d, Extra=%+v", len(output.Response), output.Extra)

	return ctx
}

func (tc *toolCallback) OnEndWithStreamOutput(ctx context.Context, runInfo *callbacks.RunInfo, output *einoschema.StreamReader[*einotool.CallbackOutput]) context.Context {
	// log.Debugf("[Tool Callback] OnEndWithStreamOutput - RunInfo: Name=%s, Type=%s, Component=%v", runInfo.Name, runInfo.Type, runInfo.Component)
	// log.Debugf("[Tool Callback] OnEndWithStreamOutput - StreamOutput received")
	return ctx
}

func (tc *toolCallback) OnError(ctx context.Context, runInfo *callbacks.RunInfo, err error) context.Context {
	log.Errorf("[Tool Callback] OnError - RunInfo: Name=%s, Type=%s, Component=%v", runInfo.Name, runInfo.Type, runInfo.Component)
	log.Errorf("[Tool Callback] OnError - Error: %v", err)
	return ctx
}
