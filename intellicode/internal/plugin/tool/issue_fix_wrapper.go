package tool

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/cloudwego/eino/components/tool"
	"github.com/cloudwego/eino/schema"

	basetool "intellicode/internal/pkg/tool"
	pbevent "intellicode/pkg/proto/event"
)

// IssueFixWrapperNew IssueFix 工具的新包装器实现
type IssueFixWrapperNew struct {
	*basetool.BaseTool
	originalTool tool.InvokableTool
}

// NewIssueFixWrapperNew 创建 IssueFix 工具包装器
func NewIssueFixWrapperNew() *IssueFixWrapperNew {
	// 创建原始工具实例
	originalTool := NewIssueFixTool()

	// 创建包装器
	wrapper := &IssueFixWrapperNew{
		BaseTool:     basetool.NewBaseTool("IssueFixer", "Fix code issues", nil),
		originalTool: originalTool,
	}

	return wrapper
}

// Info 返回工具信息
func (ifw *IssueFixWrapperNew) Info(ctx context.Context) (*schema.ToolInfo, error) {
	return ifw.originalTool.Info(ctx)
}

// InvokableRun 实现 InvokableTool 接口
func (ifw *IssueFixWrapperNew) InvokableRun(ctx context.Context, argumentsInJSON string, opts ...tool.Option) (string, error) {
	return ifw.WrapInvokableRun(ctx, argumentsInJSON, func(ctx context.Context, input string) (string, error) {
		result, err := ifw.originalTool.InvokableRun(ctx, input, opts...)
		
		// 执行成功后，发送 suggestions 事件
		if err == nil {
			pbSuggestions := ifw.parseSuggestions(input)
			if len(pbSuggestions) > 0 {
				// 通过 BaseTool 访问 emitter
				emitter := ifw.GetEventEmitter()
				if emitter != nil {
					// 直接使用 protobuf 类型
					emitter.EmitSuggestions(ctx, pbSuggestions)
				}
			}
		}
		
		return result, err
	})
}

// FormatEvent 格式化事件输出，仅显示输入，状态为 success
func (ifw *IssueFixWrapperNew) FormatEvent(input, result string, err error) string {
	if err != nil {
		return fmt.Sprintf("Failed to generate fix: %s", err.Error())
	}
	return "Fix suggestions generated"
}

// parseSuggestions 解析工具输入并转换为 Suggestion 结构体
func (ifw *IssueFixWrapperNew) parseSuggestions(input string) []*pbevent.Suggestion {
	var req IssueFixRequest
	if err := json.Unmarshal([]byte(input), &req); err != nil {
		return nil
	}

	// 转换为事件系统的 Suggestion 格式
	suggestion := &pbevent.Suggestion{
		Id:          int32(req.IssueID),
		Category:    "code_fix",
		Level:       "Major",
		FilePath:    req.FilePath,
		StartLine:   0, // 这里可以根据需要进行解析
		EndLine:     0, // 这里可以根据需要进行解析
		Description: "Code fix suggestion",
		OldString:   req.OldStr,
		NewString:   req.NewStr,
	}

	return []*pbevent.Suggestion{suggestion}
}

