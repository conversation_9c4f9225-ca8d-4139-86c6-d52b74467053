package tool

import (
	"context"
	"fmt"

	"github.com/cloudwego/eino/components/tool"
	"github.com/cloudwego/eino/schema"

	basetool "intellicode/internal/pkg/tool"
)

// IssueAnalyzerWrapper IssueAnalyzer 工具的包装器实现
type IssueAnalyzerWrapper struct {
	*basetool.BaseTool
	originalTool tool.InvokableTool
}

// NewIssueAnalyzerWrapper 创建 IssueAnalyzer 工具包装器
func NewIssueAnalyzerWrapper() *IssueAnalyzerWrapper {
	// 创建原始工具实例
	originalTool := NewIssueAnalyzerTool()

	// 创建包装器
	wrapper := &IssueAnalyzerWrapper{
		BaseTool:     basetool.NewBaseTool("IssueAnalyzer", "Analyze issues structurally", nil),
		originalTool: originalTool,
	}

	return wrapper
}

// Info 返回工具信息
func (iaw *IssueAnalyzerWrapper) Info(ctx context.Context) (*schema.ToolInfo, error) {
	return iaw.originalTool.Info(ctx)
}

// InvokableRun 实现 InvokableTool 接口
func (iaw *IssueAnalyzerWrapper) InvokableRun(ctx context.Context, argumentsInJSON string, opts ...tool.Option) (string, error) {
	return iaw.WrapInvokableRun(ctx, argumentsInJSON, func(ctx context.Context, input string) (string, error) {
		return iaw.originalTool.InvokableRun(ctx, input, opts...)
	})
}

// FormatEvent 格式化事件输出，仅显示分析完成，不显示具体内容
func (iaw *IssueAnalyzerWrapper) FormatEvent(input, result string, err error) string {
	if err != nil {
		return fmt.Sprintf("Analysis failed: %s", err.Error())
	}
	// 仅显示分析完成，不显示具体内容
	return "Code analysis completed"
}