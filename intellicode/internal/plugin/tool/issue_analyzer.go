package tool

import (
	"context"

	"github.com/cloudwego/eino/components/tool"
	"github.com/cloudwego/eino/components/tool/utils"
	"github.com/cloudwego/eino/schema"
	"github.com/getkin/kin-openapi/openapi3"
)

const (
	issueAnalyzerToolName = "IssueAnalyzer"
	issueAnalyzerToolDesc = `这是一个用于思考的工具，可以使用它在 Issue 问题分析、定位时进行结构化思考。
此工具用于对代码变更中潜在的问题进行确认和分析，确认潜在的问题是否存在。必须在结合**审查要点**得出**潜在问题**之后，再调用这个工具进一步确认问题是否成立。
为了更好地确认问题，你可以使用工具获取与潜在问题相关的代码进行关联分析。


参数说明：
- thought: 对于当前潜在问题进行思考和分析。需要说明潜在问题是什么，应该如何确认这个问题。
- action: 罗列出确定问题需要执行的动作事项。包含需要调用的工具也要罗列出来。
`

	responseMessage = "<system-reminder>按照 action 继续执行。</system-reminder>"
)

type issueAnalyzerTool struct{}

func (c *issueAnalyzerTool) process(ctx context.Context, req IssueAnalyzerRequest) (*IssueAnalyzerResponse, error) {
	return &IssueAnalyzerResponse{Message: responseMessage}, nil
}

type IssueAnalyzerRequest struct {
	Thought string `json:"thought"`
	Action  string `json:"action"`
}

type IssueAnalyzerResponse struct {
	Message string `json:"message"`
}

func NewIssueAnalyzerTool() tool.InvokableTool {
	issueAnalyzerTool := &issueAnalyzerTool{}

	toolInfo := &schema.ToolInfo{
		Name: issueAnalyzerToolName,
		Desc: issueAnalyzerToolDesc,
		ParamsOneOf: schema.NewParamsOneOfByOpenAPIV3(&openapi3.Schema{
			Type: openapi3.TypeObject,
			Properties: map[string]*openapi3.SchemaRef{
				"thought": {
					Value: &openapi3.Schema{
						Type:        openapi3.TypeString,
						Description: "",
					},
				},
				"action": {
					Value: &openapi3.Schema{
						Type:        openapi3.TypeString,
						Description: "",
					},
				},
			},
			Required: []string{"thought", "action"},
		}),
	}

	return utils.NewTool(toolInfo, issueAnalyzerTool.process)
}
