package tool

import (
	"context"
	"encoding/json"
	"intellicode/internal/plugin/consts"
	"intellicode/internal/plugin/schema"
	"log"
	"sync"

	pkgtool "intellicode/internal/pkg/tool"

	"github.com/cloudwego/eino/components/tool"
	"github.com/cloudwego/eino/components/tool/utils"
	einoschema "github.com/cloudwego/eino/schema"
	"github.com/getkin/kin-openapi/openapi3"
)

type IssueFixRequest struct {
	IssueID    int    `json:"issue_id"`
	FilePath   string `json:"file_path"`
	OldStr     string `json:"old_string"`
	NewStr     string `json:"new_string"`
	ReplaceAll bool   `json:"replace_all"`
}

type IssueFixResponse struct{}

const (
	issueFixName     = "IssueFixer"
	issueFixDescribe = `记录由 IssueRecord 工具发现的问题的修复方案。

使用方法:
- 此工具用于修复 Issue，只有当 Issue 是一个明确的缺陷且能被直接修复时，才能使用这个工具。
- 您必须提供来自 IssueRecord 工具的 issue_id 以将修复方案链接到相应的问题。
- 在编辑之前，您必须在对话中至少使用一次 IssueRecord 工具。
- 当编辑来自 Read 工具输出的文本时，请确保保持行号前缀之后显示的确切缩进（制表符/空格）。行号前缀格式为：空格 + 行号 + 制表符。制表符之后的所有内容都是要匹配的实际文件内容。永远不要在 old_string 或 new_string 中包含行号前缀的任何部分。
- 始终优先编辑代码库中的现有文件。除非明确要求，否则永远不要编写新文件。
- 只有在用户明确要求时才使用表情符号。除非被要求，否则避免向文件添加表情符号。
- 如果 old_string 在文件中不唯一，编辑将失败。要么提供更大的字符串和更多的上下文以使其唯一，要么使用 replace_all 来更改 old_string 的每个实例。
- 使用 replace_all 来替换和重命名整个文件中的字符串。此参数在您想要重命名变量时很有用。`
)

type issueFixTool struct {
	fixes []IssueFixRequest
}

func (i *issueFixTool) process(ctx context.Context, req IssueFixRequest) (*IssueFixResponse, error) {
	if i.fixes == nil {
		i.fixes = []IssueFixRequest{}
	}

	// 校验 old_string 与 new_string 不能一致
	if req.OldStr == req.NewStr {
		return nil, pkgtool.NewToolErrorf("old_string and new_string are exactly the same.")
	}

	i.fixes = append(i.fixes, req)

	return &IssueFixResponse{}, nil
}

func NewIssueFixTool() tool.InvokableTool {
	issueFixTool := &issueFixTool{fixes: []IssueFixRequest{}}

	toolInfo := &einoschema.ToolInfo{
		Name: issueFixName,
		Desc: issueFixDescribe,
		ParamsOneOf: einoschema.NewParamsOneOfByOpenAPIV3(&openapi3.Schema{
			Type: openapi3.TypeObject,
			Properties: map[string]*openapi3.SchemaRef{
				"issue_id": {
					Value: &openapi3.Schema{
						Type:        openapi3.TypeInteger,
						Description: "Issue 的 ID (来自IssueRecord tool)",
					},
				},
				"file_path": {
					Value: &openapi3.Schema{
						Type:        openapi3.TypeString,
						Description: "文件的绝对路径",
					},
				},
				"old_string": {
					Value: &openapi3.Schema{
						Type:        openapi3.TypeString,
						Description: "用于在 file_path 中进行字符串匹配的内容",
					},
				},
				"new_string": {
					Value: &openapi3.Schema{
						Type:        openapi3.TypeString,
						Description: "替换 old_string 的内容(必须与old_string不一样)",
					},
				},
				"replace_all": {
					Value: &openapi3.Schema{
						Type:        openapi3.TypeBoolean,
						Description: "是否替换文件中所有 old_string (default false)",
						Default:     false,
					},
				},
			},
			Required: []string{"issue_id", "file_path", "old_string", "new_string"},
		}),
	}

	return utils.NewTool(toolInfo, issueFixTool.process)
}

// IssueFixWrapper 包装器，用于拦截和记录 IssueFixTool 的调用，并生成 Suggestion
type IssueFixWrapper struct {
	originalTool    tool.InvokableTool
	issueWrapper    *IssueRecordWrapper
	suggestions     []schema.Suggestion
	suggestionMutex sync.RWMutex
	idCounter       int
}

// NewIssueFixWrapper 创建一个新的 IssueFixWrapper
func NewIssueFixWrapper(originalTool tool.InvokableTool, issueWrapper *IssueRecordWrapper) *IssueFixWrapper {
	return &IssueFixWrapper{
		originalTool: originalTool,
		issueWrapper: issueWrapper,
		suggestions:  make([]schema.Suggestion, 0),
		idCounter:    1,
	}
}

// Info 返回工具信息
func (w *IssueFixWrapper) Info(ctx context.Context) (*einoschema.ToolInfo, error) {
	return w.originalTool.Info(ctx)
}

// InvokableRun 执行工具调用并记录 Suggestion
func (w *IssueFixWrapper) InvokableRun(ctx context.Context, input string, opts ...tool.Option) (string, error) {
	// 1. 调用原始工具
	result, err := w.originalTool.InvokableRun(ctx, input, opts...)

	// 2. 如果调用成功，生成 Suggestion
	if err == nil {
		if suggestion, parseErr := w.createSuggestion(input); parseErr == nil {
			// 安全地添加建议
			func() {
				defer func() {
					if r := recover(); r != nil {
						log.Printf("生成 Suggestion 时发生 panic: %v", r)
					}
				}()
				w.addSuggestion(suggestion)
			}()
		} else {
			// 记录解析错误但不影响主流程
			log.Printf("解析 IssueFixTool 输入失败: %v", parseErr)
		}
	}

	return result, err
}

// createSuggestion 根据 IssueFixRequest 和对应的 IssueRecord 创建 Suggestion
func (w *IssueFixWrapper) createSuggestion(input string) (schema.Suggestion, error) {
	var req IssueFixRequest
	if err := json.Unmarshal([]byte(input), &req); err != nil {
		return schema.Suggestion{}, err
	}

	// 获取对应的 IssueRecord
	var matchedIssue IssueRecordItem
	var found bool
	if w.issueWrapper != nil {
		records := w.issueWrapper.GetRecords()
		for _, record := range records {
			if record.ID == req.IssueID {
				matchedIssue = record
				found = true
				break
			}
		}
	}

	// 创建 Suggestion
	suggestion := schema.Suggestion{
		ID:          w.getNextID(),
		FilePath:    req.FilePath,
		OldStr:      req.OldStr,
		NewStr:      req.NewStr,
		Description: "代码修复建议",
	}

	// 如果找到了对应的 IssueRecord，使用其信息
	if found {
		suggestion.Category = matchedIssue.Category
		suggestion.Level = matchedIssue.Level
		suggestion.StartLine = matchedIssue.StartLine
		suggestion.EndLine = matchedIssue.EndLine
		suggestion.Description = matchedIssue.Description
	} else {
		// 如果没有找到对应的 IssueRecord，使用默认值
		suggestion.Category = "code_fix"
		suggestion.Level = consts.LevelInfo
		suggestion.StartLine = 0
		suggestion.EndLine = 0
	}

	return suggestion, nil
}

// addSuggestion 添加建议（内部方法）
func (w *IssueFixWrapper) addSuggestion(suggestion schema.Suggestion) {
	w.suggestionMutex.Lock()
	defer w.suggestionMutex.Unlock()
	w.suggestions = append(w.suggestions, suggestion)
}

// GetSuggestions 获取所有建议（供 DiffAgent 调用）
func (w *IssueFixWrapper) GetSuggestions() []schema.Suggestion {
	w.suggestionMutex.RLock()
	defer w.suggestionMutex.RUnlock()
	// 返回副本防止外部修改
	suggestions := make([]schema.Suggestion, len(w.suggestions))
	copy(suggestions, w.suggestions)
	return suggestions
}

// getNextID 获取下一个 ID
func (w *IssueFixWrapper) getNextID() int {
	w.suggestionMutex.Lock()
	defer w.suggestionMutex.Unlock()
	id := w.idCounter
	w.idCounter++
	return id
}
