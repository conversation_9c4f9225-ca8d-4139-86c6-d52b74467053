package tool

import (
	"context"
	"encoding/json"
	"fmt"
	"intellicode/internal/plugin/consts"
	"intellicode/internal/pkg/emitter"
	pb "intellicode/pkg/proto/event"
	"log"
	"strings"
	"sync"

	einotool "github.com/cloudwego/eino/components/tool"
	"github.com/cloudwego/eino/components/tool/utils"
	"github.com/cloudwego/eino/schema"
	"github.com/getkin/kin-openapi/openapi3"
)

type IssueRecordItem struct {
	ID          int          `json:"id"`       // 唯一标识符，自增
	Category    string       `json:"category"` // code_defect, refactor
	Level       consts.Level `json:"level"`    // info, warning, error
	CodeSnippet string       `json:"code_snippet"`
	FilePath    string       `json:"file_path"`
	StartLine   int          `json:"start_line"`
	EndLine     int          `json:"end_line"`
	Description string       `json:"description"`
}

type IssueRecordRequest struct {
	Issues []IssueRecordItem `json:"issues"`
}

type IssueRecordResponse struct {
	Message string `json:"message"`
}

type issueRecordTool struct {
	issues    []IssueRecordItem
	idCounter int
	mutex     sync.RWMutex // 添加互斥锁保护并发访问
}

func (i *issueRecordTool) process(ctx context.Context, req IssueRecordRequest) (*IssueRecordResponse, error) {
	i.mutex.Lock()
	defer i.mutex.Unlock()

	if i.issues == nil {
		i.issues = []IssueRecordItem{}
	}

	// 为每个 issue 分配唯一 ID
	var messages []string
	for _, issue := range req.Issues {
		issue.ID = i.idCounter
		i.issues = append(i.issues, issue)
		messages = append(messages, fmt.Sprintf("<system-reminder>Issue %d 已经被记录，请调用 IssueFixer 修复这个问题</system-reminder>", i.idCounter))
		i.idCounter++
	}

	return &IssueRecordResponse{
		Message: strings.Join(messages, "\n"),
	}, nil
}

const (
	issueRecordToolName = "IssueRecord"
	issueRecordToolDesc = `使用此工具来记录代码审查中发现的问题。你必须要经过详细的分析和定位，确认的确是可以被修复的问题，才能使用这个工具记录问题。
但你记录完成 Issue 之后，需要立即调用 IssueFixer 修复这个问题。
`
)

func NewIssueRecordTool() einotool.InvokableTool {
	tool := &issueRecordTool{issues: []IssueRecordItem{}, idCounter: 1}

	toolInfo := &schema.ToolInfo{
		Name: issueRecordToolName,
		Desc: issueRecordToolDesc,
		ParamsOneOf: schema.NewParamsOneOfByOpenAPIV3(&openapi3.Schema{
			Type: openapi3.TypeObject,
			Properties: map[string]*openapi3.SchemaRef{
				"issues": {
					Value: &openapi3.Schema{
						Type: openapi3.TypeArray,
						Items: &openapi3.SchemaRef{
							Value: &openapi3.Schema{
								Type: openapi3.TypeObject,
								Properties: map[string]*openapi3.SchemaRef{
									"category": {
										Value: &openapi3.Schema{
											Type:        openapi3.TypeString,
											Description: "问题分类",
										},
									},
									"level": {
										Value: &openapi3.Schema{
											Type:        openapi3.TypeString,
											Enum:        []any{string(consts.LevelInfo), string(consts.LevelMinor), string(consts.LevelMajor), string(consts.LevelCritical), string(consts.LevelBlocker)},
											Description: "问题严重级别",
										},
									},
									"code_snippet": {
										Value: &openapi3.Schema{
											Type:        openapi3.TypeString,
											Description: "问题代码片段，必须有足够的内容确保**文本搜索**能够唯一匹配到问题代码",
										},
									},
									"file_path": {
										Value: &openapi3.Schema{
											Type:        openapi3.TypeString,
											Description: "文件路径",
										},
									},
									"start_line": {
										Value: &openapi3.Schema{
											Type:        openapi3.TypeInteger,
											Description: "起始行号",
										},
									},
									"end_line": {
										Value: &openapi3.Schema{
											Type:        openapi3.TypeInteger,
											Description: "结束行号",
										},
									},
									"description": {
										Value: &openapi3.Schema{
											Type:        openapi3.TypeString,
											Description: "问题描述",
										},
									},
								},
								Required: []string{"category", "level", "code_snippet", "file_path", "start_line", "end_line", "description"},
							},
						},
						Description: "问题列表",
					},
				},
			},
			Required: []string{"issues"},
		}),
	}
	return utils.NewTool(toolInfo, tool.process)
}

// IssueRecordWrapper 包装器，用于拦截和记录 IssueRecordTool 的调用
type IssueRecordWrapper struct {
	originalTool einotool.InvokableTool
	records      []IssueRecordItem
	recordMutex  sync.RWMutex
	emitter      emitter.EventEmitter
}

// NewIssueRecordWrapper 创建一个新的 IssueRecordWrapper
func NewIssueRecordWrapper() *IssueRecordWrapper {
	originalTool := NewIssueRecordTool()
	return &IssueRecordWrapper{
		originalTool: originalTool,
		records:      make([]IssueRecordItem, 0),
		emitter:      emitter.NewNopEmitter(),
	}
}

// Info 返回工具信息
func (w *IssueRecordWrapper) Info(ctx context.Context) (*schema.ToolInfo, error) {
	return w.originalTool.Info(ctx)
}

// InvokableRun 执行工具调用并记录结果
func (w *IssueRecordWrapper) InvokableRun(ctx context.Context, input string, opts ...einotool.Option) (string, error) {
	// 1. 发送 toolstart 事件
	if w.ShouldShowInUI() {
		w.emitter.EmitToolStart(ctx, w.GetName(), input)
	}
	
	// 2. 调用原始工具
	result, err := w.originalTool.InvokableRun(ctx, input, opts...)

	// 3. 记录过程中的错误不应影响主流程
	if err == nil {
		if records, parseErr := w.parseIssueRecords(input); parseErr == nil {
			// 安全地添加记录
			func() {
				defer func() {
					if r := recover(); r != nil {
						log.Printf("记录 IssueRecord 时发生 panic: %v", r)
					}
				}()
				w.addRecords(records)
			}()
		} else {
			// 记录解析错误但不影响主流程
			log.Printf("解析 IssueRecord 输入失败: %v", parseErr)
		}
	}

	// 4. 发送 toolend 事件
	if w.ShouldShowInUI() {
		status := pb.ToolStatus_TOOL_STATUS_SUCCESS
		if err != nil {
			status = pb.ToolStatus_TOOL_STATUS_ERROR
		}
		output := w.FormatEvent(input, result, err)
		w.emitter.EmitToolEnd(ctx, w.GetName(), input, output, status)
	}

	return result, err
}

// parseIssueRecords 解析输入参数获取 IssueRecordItem
func (w *IssueRecordWrapper) parseIssueRecords(input string) ([]IssueRecordItem, error) {
	var req IssueRecordRequest
	if err := json.Unmarshal([]byte(input), &req); err != nil {
		return nil, err
	}
	return req.Issues, nil
}

// addRecords 添加记录（内部方法）
func (w *IssueRecordWrapper) addRecords(items []IssueRecordItem) {
	w.recordMutex.Lock()
	defer w.recordMutex.Unlock()
	w.records = append(w.records, items...)
}

// GetRecords 获取所有记录（供 DiffAgent 调用）
func (w *IssueRecordWrapper) GetRecords() []IssueRecordItem {
	w.recordMutex.RLock()
	defer w.recordMutex.RUnlock()
	// 返回副本防止外部修改
	records := make([]IssueRecordItem, len(w.records))
	copy(records, w.records)
	return records
}

// ToolWrapper 接口方法
func (w *IssueRecordWrapper) SetEventEmitter(emitter emitter.EventEmitter) {
	w.emitter = emitter
}

func (w *IssueRecordWrapper) GetName() string {
	return "IssueRecord"
}

func (w *IssueRecordWrapper) ShouldShowInUI() bool {
	return true
}

func (w *IssueRecordWrapper) FormatEvent(input, result string, err error) string {
	if err != nil {
		return fmt.Sprintf("Failed to record issue: %s", err.Error())
	}
	return "Issue recorded successfully"
}
