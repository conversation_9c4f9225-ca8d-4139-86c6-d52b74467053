package code

import (
	"path/filepath"
	"slices"
	"strings"

	"github.com/pkg/errors"
)

// Language 表示编程语言类型
type Language string

// 编程语言常量
const (
	LanguageGo         Language = "go"
	LanguageJava       Language = "java"
	LanguagePython     Language = "python"
	LanguageJavaScript Language = "javascript"
	LanguageTypeScript Language = "typescript"
	LanguageC          Language = "c"
	LanguageCPlusPlus  Language = "cpp"
	LanguageCSharp     Language = "csharp"
	LanguageRust       Language = "rust"
	LanguageKotlin     Language = "kotlin"
	LanguageSwift      Language = "swift"
	LanguagePHP        Language = "php"
	LanguageRuby       Language = "ruby"
	LanguageScala      Language = "scala"
	LanguageR          Language = "r"
	LanguageMatlab     Language = "matlab"
	LanguageShell      Language = "shell"
	LanguageBash       Language = "bash"
	LanguageSQL        Language = "sql"
	LanguageHTML       Language = "html"
	LanguageCSS        Language = "css"
	LanguageXML        Language = "xml"
	LanguageJSON       Language = "json"
	LanguageYAML       Language = "yaml"
	LanguageMarkdown   Language = "markdown"
	LanguageDockerfile Language = "dockerfile"
	LanguageMakefile   Language = "makefile"
	LanguageProto      Language = "proto"
	LanguageUnknown    Language = "unknown"
)

// 字符串到 Language 的映射表
var stringToLanguage = map[string]Language{
	"go":         LanguageGo,
	"java":       LanguageJava,
	"python":     LanguagePython,
	"javascript": LanguageJavaScript,
	"typescript": LanguageTypeScript,
	"c":          LanguageC,
	"cpp":        LanguageCPlusPlus,
	"csharp":     LanguageCSharp,
	"rust":       LanguageRust,
	"kotlin":     LanguageKotlin,
	"swift":      LanguageSwift,
	"php":        LanguagePHP,
	"ruby":       LanguageRuby,
	"scala":      LanguageScala,
	"r":          LanguageR,
	"matlab":     LanguageMatlab,
	"shell":      LanguageShell,
	"bash":       LanguageBash,
	"sql":        LanguageSQL,
	"html":       LanguageHTML,
	"css":        LanguageCSS,
	"xml":        LanguageXML,
	"json":       LanguageJSON,
	"yaml":       LanguageYAML,
	"markdown":   LanguageMarkdown,
	"dockerfile": LanguageDockerfile,
	"makefile":   LanguageMakefile,
	"proto":      LanguageProto,
	"unknown":    LanguageUnknown,
}

// GetLanguage 将字符串转换为 Language 类型
func GetLanguage(langStr string) (Language, error) {
	lang, ok := stringToLanguage[strings.ToLower(langStr)]
	if !ok {
		return LanguageUnknown, errors.Errorf("unknown language: %s", langStr)
	}
	return lang, nil
}

// IsSupported 通过字符串判断语言是否支持
func IsSupported(langStr string) bool {
	lang, err := GetLanguage(langStr)
	if err != nil {
		return false
	}
	return IsLanguageSupported(lang)
}

// IsLanguageSupported 判断输入的语言是否在支持列表中
func IsLanguageSupported(lang Language) bool {
	return slices.Contains(supportedLanguages, lang)
}

// 支持的编程语言列表
var supportedLanguages = []Language{
	LanguagePython,
	LanguageJava,
	LanguageJavaScript,
	LanguageTypeScript,
	LanguageHTML,
	LanguageCSS,
	LanguageGo,
	LanguageCPlusPlus,
}

// 扩展名到语言的映射表
var extensionToLanguage = map[string]Language{
	".go":    LanguageGo,
	".java":  LanguageJava,
	".py":    LanguagePython,
	".js":    LanguageJavaScript,
	".ts":    LanguageTypeScript,
	".c":     LanguageC,
	".cpp":   LanguageCPlusPlus,
	".cc":    LanguageCPlusPlus,
	".cxx":   LanguageCPlusPlus,
	".cs":    LanguageCSharp,
	".rs":    LanguageRust,
	".kt":    LanguageKotlin,
	".swift": LanguageSwift,
	".php":   LanguagePHP,
	".rb":    LanguageRuby,
	".scala": LanguageScala,
	".r":     LanguageR,
	".m":     LanguageMatlab,
	".sh":    LanguageShell,
	".sql":   LanguageSQL,
	".html":  LanguageHTML,
	".htm":   LanguageHTML,
	".css":   LanguageCSS,
	".xml":   LanguageXML,
	".json":  LanguageJSON,
	".yaml":  LanguageYAML,
	".yml":   LanguageYAML,
	".md":    LanguageMarkdown,
	".proto": LanguageProto,
}

// 特殊文件名到语言的映射表
var filenameToLanguage = map[string]Language{
	"dockerfile": LanguageDockerfile,
	"makefile":   LanguageMakefile,
}

// DetectLanguageFromPath 根据文件路径推断编程语言
func DetectLanguageFromPath(filePath string) Language {
	// 获取扩展名
	var ext string
	if strings.HasPrefix(filePath, ".") && !strings.Contains(filePath[1:], ".") {
		// 处理只有后缀的情况，如 ".go"
		ext = strings.ToLower(filePath)
	} else {
		// 处理正常的文件路径
		ext = strings.ToLower(filepath.Ext(filePath))
	}

	// 根据扩展名查找语言
	if lang, exists := extensionToLanguage[ext]; exists {
		return lang
	}

	// 检查特殊文件名
	filename := strings.ToLower(filepath.Base(filePath))
	if lang, exists := filenameToLanguage[filename]; exists {
		return lang
	}

	return LanguageUnknown
}
