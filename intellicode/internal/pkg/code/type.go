package code

import (
	"crypto/sha256"
	"fmt"
	"strings"

	"github.com/pkg/errors"
	"github.com/sourcegraph/go-diff/diff"
)

// CodeChange 表示代码变更的集合
type CodeChange struct {
	Sha   string // 代码字符串对应唯一哈希值
	Files []CodePatch
}

func (c CodeChange) AllFilesPath() []string {
	var paths []string
	for _, file := range c.Files {
		paths = append(paths, file.Path)
	}
	return paths
}

// CodeHunk 表示代码变更的一个片段
type CodeHunk struct {
	SourceStartLine int
	SourceEndLine   int
	TargetStartLine int
	TargetEndLine   int
	SourceFilePath  string
	TargetFilePath  string
	SourceContent   string
	TargetContent   string
	DiffContent     string
}

func modeFromStat(stat diff.Stat) string {
	// 如果只有删除操作，没有添加操作，则为删除文件
	if stat.Deleted > 0 && stat.Added == 0 {
		return "deleted"
	}
	// 如果只有添加操作，没有删除操作，则为新增文件
	if stat.Added > 0 && stat.Deleted == 0 {
		return "added"
	}
	// 如果既有添加又有删除，或者有变更操作，则为修改文件
	if stat.Added > 0 || stat.Deleted > 0 || stat.Changed > 0 {
		return "changed"
	}
	// 默认情况下返回changed
	return "changed"
}

// filePathFromFileDiff 会考虑 Mode 获取文件，added 获取 newfile，deleted 获取 oldfile，changed 获取 newfile
func filePathFromFileDiff(fileDiff *diff.FileDiff) string {
	mode := modeFromStat(fileDiff.Stat())
	var diffFileName string
	switch mode {
	case "added":
		diffFileName = fileDiff.NewName
	case "deleted":
		diffFileName = fileDiff.OrigName
	case "changed":
		diffFileName = fileDiff.NewName
	default:
		diffFileName = fileDiff.NewName
	}

	diffFileName = strings.TrimPrefix(diffFileName, "a/")
	diffFileName = strings.TrimPrefix(diffFileName, "b/")
	return diffFileName
}

// CodePatch 代表代码变更的一个文件
type CodePatch struct {
	Path        string // 相对路径 foo/bar/a.go
	Language    Language
	DiffContent string // git diff
	Hunks       []CodeHunk
	Mode        string // added/changed/deleted
}

func NewCodeChange(diffContent string) (*CodeChange, error) {
	diffByte := []byte(diffContent)
	fileDiffs, err := diff.ParseMultiFileDiff(diffByte)
	if err != nil {
		return nil, errors.Wrap(err, "failed to parse multi file diff")
	}

	var codeFiles []CodePatch
	for _, fileDiff := range fileDiffs {
		var hunks []CodeHunk
		var fileDiffContent []byte
		for _, hunk := range fileDiff.Hunks {
			// new hunk
			hunks = append(hunks, CodeHunk{
				SourceStartLine: int(hunk.OrigStartLine),
				SourceEndLine:   int(hunk.OrigStartLine + hunk.OrigLines - 1),
				TargetStartLine: int(hunk.NewStartLine),
				TargetEndLine:   int(hunk.NewStartLine + hunk.NewLines - 1),
				SourceFilePath:  fileDiff.OrigName,
				TargetFilePath:  fileDiff.NewName,
				DiffContent:     string(hunk.Body),
			})

			// 准备 CodePatch 内容
			fileDiffContent = append(fileDiffContent, hunk.Body...)
		}

		codeFilePath := filePathFromFileDiff(fileDiff)
		codeFile := CodePatch{
			Path:        codeFilePath,
			Language:    DetectLanguageFromPath(codeFilePath),
			DiffContent: string(fileDiffContent),
			Hunks:       hunks,
			Mode:        modeFromStat(fileDiff.Stat()),
		}
		codeFiles = append(codeFiles, codeFile)
	}

	hash := sha256.Sum256(diffByte)
	sha := fmt.Sprintf("%x", hash)

	return &CodeChange{
		Sha:   sha,
		Files: codeFiles,
	}, nil
}
