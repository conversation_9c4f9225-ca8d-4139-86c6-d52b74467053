package repo

import (
	"context"
	"fmt"
	"os/exec"
	"strings"
	"time"

	"intellicode/internal/pkg/code"
	"intellicode/internal/pkg/log"

	"github.com/go-git/go-git/v5"
	"github.com/go-git/go-git/v5/plumbing"
	"github.com/pkg/errors"
)

type GitRepo struct {
	repo *git.Repository
	path string
}

// GetCodeChangeOptions 配置 GetCodeChange 方法的选项
type GetCodeChangeOptions struct {
	FilterUnsupportedLanguages bool // 是否过滤不支持的语言文件，默认为 true
}

// GetCodeChangeOption 用于配置 GetCodeChangeOptions 的函数类型
type GetCodeChangeOption func(*GetCodeChangeOptions)

// WithFilterUnsupportedLanguages 设置是否过滤不支持的语言文件
func WithFilterUnsupportedLanguages(filter bool) GetCodeChangeOption {
	return func(opts *GetCodeChangeOptions) {
		opts.FilterUnsupportedLanguages = filter
	}
}

// defaultGetCodeChangeOptions 返回默认的配置选项
func defaultGetCodeChangeOptions() *GetCodeChangeOptions {
	return &GetCodeChangeOptions{
		FilterUnsupportedLanguages: true, // 默认过滤不支持的语言
	}
}

func NewGitRepo(path string) (*GitRepo, error) {
	repo, err := git.PlainOpen(path)
	if err != nil {
		return nil, err
	}
	return &GitRepo{repo: repo, path: path}, nil
}

// GetCodeChange 会获取 relPathFiles 指定的文件对应的代码变更。
// relPathFiles 必须是 Repo 中的相对路径
func (g *GitRepo) GetCodeChange(relPathFiles []string, opts ...GetCodeChangeOption) (code.CodeChange, error) {
	log.Debugf("repo :%s GetCodeChanges relativeFiles: %+v", g.path, relPathFiles)
	if len(relPathFiles) == 0 {
		return code.CodeChange{}, errors.New("no relative file specified")
	}
	_, err := g.repo.Worktree()
	if err == git.ErrIsBareRepository {
		log.Infof("%s is bare repository", g.path)
		return code.CodeChange{}, errors.New("bare repository not supported")
	}
	if err != nil {
		return code.CodeChange{}, errors.Wrap(err, "failed to get worktree")
	}

	// 应用配置选项
	options := defaultGetCodeChangeOptions()
	for _, opt := range opts {
		opt(options)
	}

	// 根据配置决定是否过滤不支持的语言文件
	filesToProcess := relPathFiles
	skipedFiles := []string{}
	if options.FilterUnsupportedLanguages {
		var supportedFiles []string
		for _, filePath := range relPathFiles {
			lang := code.DetectLanguageFromPath(filePath)
			if code.IsLanguageSupported(lang) {
				supportedFiles = append(supportedFiles, filePath)
			} else {
				skipedFiles = append(skipedFiles, filePath)
			}
		}

		if len(supportedFiles) == 0 {
			return code.CodeChange{}, errors.New("no supported language files found")
		}
		filesToProcess = supportedFiles
	}
	log.Debugf("skipping unsupported language files: %s ", skipedFiles)
	if len(filesToProcess) == 0 {
		return code.CodeChange{}, errors.Errorf("no files to process in %s after filter", relPathFiles)
	}

	diffContent, err := g.getRawDiff(filesToProcess)
	if err != nil {
		return code.CodeChange{}, errors.Wrap(err, "failed to get raw diff")
	}

	codeChange, err := code.NewCodeChange(diffContent)
	if err != nil {
		return code.CodeChange{}, errors.Wrap(err, "failed to create code change")
	}

	return *codeChange, nil
}

// GetRawDiff 获取 git diff 内容
// getDiffForTrackedFiles 获取已跟踪文件的 diff 内容
func (g *GitRepo) getDiffForTrackedFiles(trackedFiles []string) (string, error) {
	// 如果没有已跟踪文件，返回空字符串
	if len(trackedFiles) == 0 {
		return "", nil
	}

	// 构建 git diff 命令参数
	cmdArgs := []string{"diff", "--"}
	cmdArgs = append(cmdArgs, trackedFiles...)

	log.Debugf("git diff command for tracked files: git %s", strings.Join(cmdArgs, " "))

	// 创建带超时的上下文
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 创建 git diff 命令
	cmd := exec.CommandContext(ctx, "git", cmdArgs...)
	cmd.Dir = g.path // 设置工作目录为 git 仓库路径

	// 执行命令并捕获输出
	output, err := cmd.CombinedOutput()
	if err != nil {
		// 检查是否是超时错误
		if ctx.Err() == context.DeadlineExceeded {
			return "", errors.New("git diff command timed out")
		}

		// 检查退出码
		if exitError, ok := err.(*exec.ExitError); ok {
			// 如果退出码不为 0，记录错误信息
			log.Debugf("git diff error (exit code %d): %s", exitError.ExitCode(), string(output))
			return "", errors.Wrapf(err, "git diff command failed with exit code %d: %s", exitError.ExitCode(), string(output))
		}

		return "", errors.Wrap(err, "failed to execute git diff command")
	}

	return string(output), nil
}

// getDiffForUntrackedFile 获取单个未跟踪文件的 diff 内容
func (g *GitRepo) getDiffForUntrackedFile(file string) (string, error) {
	// 对于 untracked 文件，使用 git diff --no-index /dev/null <file>
	untrackedCmdArgs := []string{"diff", "--no-index", "/dev/null", file}
	log.Debugf("git diff command for untracked file %s: git %s", file, strings.Join(untrackedCmdArgs, " "))

	// 创建带超时的上下文
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 创建 git diff 命令
	cmd := exec.CommandContext(ctx, "git", untrackedCmdArgs...)
	cmd.Dir = g.path // 设置工作目录为 git 仓库路径

	// 执行命令并捕获输出
	output, err := cmd.CombinedOutput()
	// 注意：git diff --no-index 在有差异时会返回退出码 1，这是正常的
	if err != nil {
		// 检查是否是超时错误
		if ctx.Err() == context.DeadlineExceeded {
			return "", errors.New("git diff command timed out")
		}

		// 检查退出码
		if exitError, ok := err.(*exec.ExitError); ok {
			// 对于 git diff --no-index，退出码 1 表示有差异，这是正常的
			if exitError.ExitCode() == 1 {
				return string(output), nil
			} else {
				// 其他错误码，记录错误信息
				log.Debugf("git diff error for untracked file %s (exit code %d): %s", file, exitError.ExitCode(), string(output))
				return "", errors.Wrapf(err, "git diff command failed for untracked file %s with exit code %d: %s", file, exitError.ExitCode(), string(output))
			}
		} else {
			return "", errors.Wrapf(err, "failed to execute git diff command for untracked file %s", file)
		}
	}

	// 如果没有差异，输出为空
	return string(output), nil
}

// files: 指定的文件路径，不能为空
// 返回 git diff 的原始内容，支持 untracked 文件和没有历史 commit 的仓库
func (g *GitRepo) getRawDiff(files []string) (string, error) {
	// 检查是否为 bare repository
	_, err := g.repo.Worktree()
	if err == git.ErrIsBareRepository {
		log.Infof("%s is bare repository, cannot get diff", g.path)
		return "", errors.New("cannot get diff from bare repository")
	}

	// 检查参数
	if len(files) == 0 {
		return "", errors.New("files parameter cannot be empty")
	}

	// 检查是否有历史 commit
	hasCommits := true
	_, err = g.repo.Head()
	if err != nil {
		if errors.Is(err, plumbing.ErrReferenceNotFound) {
			log.Infof("No head reference found, repository has no commits")
			hasCommits = false
		} else {
			return "", errors.Wrap(err, "failed to get repository HEAD")
		}
	}

	// 分离 untracked 文件和已跟踪文件
	untrackedFiles := []string{}
	trackedFiles := []string{}

	if hasCommits {
		// 获取 untracked 文件列表
		cmdLs := exec.Command("git", "ls-files", "--others", "--exclude-standard")
		cmdLs.Dir = g.path
		outputLs, err := cmdLs.CombinedOutput()
		if err != nil {
			return "", errors.Wrap(err, "failed to get untracked files")
		}

		// 解析 untracked 文件列表
		untrackedList := strings.Split(strings.TrimSpace(string(outputLs)), "\n")
		untrackedMap := make(map[string]bool)
		for _, file := range untrackedList {
			if file != "" {
				untrackedMap[file] = true
			}
		}

		// 分类文件
		for _, file := range files {
			if untrackedMap[file] {
				untrackedFiles = append(untrackedFiles, file)
			} else {
				trackedFiles = append(trackedFiles, file)
			}
		}
	} else {
		// 没有历史 commit，所有文件都视为 untracked
		untrackedFiles = files
	}

	// 结果缓冲区
	var resultBuffer strings.Builder

	// 处理已跟踪文件
	if len(trackedFiles) > 0 {
		trackedDiff, err := g.getDiffForTrackedFiles(trackedFiles)
		if err != nil {
			return "", errors.Wrap(err, "failed to get diff for tracked files")
		}
		resultBuffer.WriteString(trackedDiff)
	}

	// 处理 untracked 文件
	for _, file := range untrackedFiles {
		untrackedDiff, err := g.getDiffForUntrackedFile(file)
		if err != nil {
			return "", errors.Wrapf(err, "failed to get diff for untracked file %s", file)
		}
		resultBuffer.WriteString(untrackedDiff)
	}

	return resultBuffer.String(), nil
}

// GetStatus 获取仓库状态，返回与 git status --porcelain 格式一致的字符串
func (g *GitRepo) GetStatus() (string, error) {
	log.Debugf("repo: %s GetStatus", g.path)
	
	// 检查是否为 bare repository
	worktree, err := g.repo.Worktree()
	if err == git.ErrIsBareRepository {
		log.Infof("%s is bare repository", g.path)
		return "", errors.New("bare repository not supported")
	}
	if err != nil {
		return "", errors.Wrap(err, "failed to get worktree")
	}

	// 检查是否有历史 commit
	_, err = g.repo.Head()
	if err != nil {
		if errors.Is(err, plumbing.ErrReferenceNotFound) {
			log.Debugf("No head reference found, repository has no commits")
			// 对于没有提交历史的仓库，使用 Preload 策略获取所有文件状态
			status, err := worktree.StatusWithOptions(git.StatusOptions{Strategy: git.Preload})
			if err != nil {
				return "", errors.Wrap(err, "failed to get status for repository with no commits")
			}
			return g.statusToPorcelain(status), nil
		}
		return "", errors.Wrap(err, "failed to get repository HEAD")
	}

	// 获取状态
	status, err := worktree.Status()
	if err != nil {
		return "", errors.Wrap(err, "failed to get status")
	}

	// 转换为 porcelain 格式
	return g.statusToPorcelain(status), nil
}

// statusToPorcelain 将 go-git 的状态转换为 git status --porcelain 格式
func (g *GitRepo) statusToPorcelain(status git.Status) string {
	var result strings.Builder
	
	// 遍历所有文件状态
	for filePath, fileStatus := range status {
		// 跳过未修改的文件
		if fileStatus.Staging == git.Unmodified && fileStatus.Worktree == git.Unmodified {
			continue
		}
		
		// 构建 porcelain 格式的状态行
		var line string
		
		// 处理重命名情况
		if fileStatus.Staging == git.Renamed {
			line = fmt.Sprintf("%c%c %s -> %s\n", 
				g.statusCodeToPorcelain(fileStatus.Staging),
				g.statusCodeToPorcelain(fileStatus.Worktree),
				fileStatus.Extra, // 原文件名
				filePath)         // 新文件名
		} else {
			line = fmt.Sprintf("%c%c %s\n", 
				g.statusCodeToPorcelain(fileStatus.Staging),
				g.statusCodeToPorcelain(fileStatus.Worktree),
				filePath)
		}
		
		result.WriteString(line)
	}
	
	return result.String()
}

// statusCodeToPorcelain 将 go-git 的状态码转换为 porcelain 格式字符
func (g *GitRepo) statusCodeToPorcelain(code git.StatusCode) byte {
	switch code {
	case git.Unmodified:
		return ' '
	case git.Untracked:
		return '?'
	case git.Modified:
		return 'M'
	case git.Added:
		return 'A'
	case git.Deleted:
		return 'D'
	case git.Renamed:
		return 'R'
	case git.Copied:
		return 'C'
	case git.UpdatedButUnmerged:
		return 'U'
	default:
		return '?'
	}
}

// RemoteURL 获取 Git 仓库的远程 URL
// 如果获取远程 URL 失败，则返回本地路径
func (g *GitRepo) RemoteURL() string {
	remotes, err := g.repo.Remotes()
	if err != nil || len(remotes) == 0 {
		// 如果获取远程URL失败，则使用本地路径
		log.Debugf("Getting remote URL failed, using local path: %s", g.path)
		return g.path
	}

	// 获取 origin 远程仓库的 URL
	for _, remote := range remotes {
		if remote.Config().Name == "origin" {
			urls := remote.Config().URLs
			if len(urls) > 0 {
				log.Debugf("Getting remote URL: %s", urls[0])
				return urls[0]
			}
		}
	}

	// 如果没有找到 origin，使用第一个远程仓库的 URL
	if len(remotes) > 0 {
		urls := remotes[0].Config().URLs
		if len(urls) > 0 {
			log.Debugf("Getting remote URL: %s", urls[0])
			return urls[0]
		}
	}

	// 如果所有方法都失败，则使用本地路径
	log.Debugf("Getting remote URL failed, using local path: %s", g.path)
	return g.path
}
