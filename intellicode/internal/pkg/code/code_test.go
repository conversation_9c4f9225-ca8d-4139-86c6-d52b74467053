package code

import (
	"crypto/sha256"
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestGetLanguage(t *testing.T) {
	tests := []struct {
		name    string
		input   string
		expected Language
		expectErr bool
	}{
		{name: "Valid Language - Go", input: "go", expected: LanguageGo, expectErr: false},
		{name: "Valid Language - Python (lowercase)", input: "python", expected: LanguagePython, expectErr: false},
		{name: "Valid Language - Java (uppercase)", input: "JAVA", expected: LanguageJava, expectErr: false},
		{name: "Unknown Language", input: "unknown_lang", expected: LanguageUnknown, expectErr: true},
		{name: "Empty String", input: "", expected: LanguageUnknown, expectErr: true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			lang, err := GetLanguage(tt.input)
			if tt.expectErr {
				assert.Error(t, err)
				assert.Equal(t, tt.expected, lang)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, lang)
			}
		})
	}
}

func TestNewCodeChange(t *testing.T) {
	tests := []struct {
		name        string
		diffContent string
		wantFiles   int
		wantErr     bool
		errMsg      string
	}{
		{
			name: "Valid single file diff",
			diffContent: `diff --git a/test.go b/test.go
index 1234567..abcdefg 100644
--- a/test.go
+++ b/test.go
@@ -1,3 +1,4 @@
 package main
 
+import "fmt"
 func main() {
 }`,
			wantFiles: 1,
			wantErr:   false,
		},
		{
			name: "Valid multi file diff",
			diffContent: `diff --git a/file1.go b/file1.go
index 1234567..abcdefg 100644
--- a/file1.go
+++ b/file1.go
@@ -1,2 +1,3 @@
 package main
+import "fmt"
 func main() {}
diff --git a/file2.py b/file2.py
index 2345678..bcdefgh 100644
--- a/file2.py
+++ b/file2.py
@@ -1,2 +1,3 @@
 def hello():
+    print("world")
     pass`,
			wantFiles: 2,
			wantErr:   false,
		},
		{
			name: "Empty diff content",
			diffContent: "",
			wantFiles: 0,
			wantErr:   false,
		},

		{
			name: "Diff with file rename",
			diffContent: `diff --git a/old_name.go b/new_name.go
similarity index 100%
rename from old_name.go
rename to new_name.go
index 1234567..abcdefg 100644
--- a/old_name.go
+++ b/new_name.go
@@ -1,2 +1,3 @@
 package main
+// renamed file
 func main() {}`,
			wantFiles: 1,
			wantErr:   false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := NewCodeChange(tt.diffContent)

			if tt.wantErr {
				assert.Error(t, err)
				if tt.errMsg != "" && err != nil {
					assert.Contains(t, err.Error(), tt.errMsg)
				}
				assert.Nil(t, got)
				return
			}

			assert.NoError(t, err)
			assert.NotNil(t, got)
			if got != nil {
				assert.Len(t, got.Files, tt.wantFiles)
			}

			// 验证每个文件的基本结构
			if got != nil {
				for _, file := range got.Files {
				assert.NotEmpty(t, file.Path)
				// DiffContent 应该只包含 hunk 内容，不包含 git diff 头部
				assert.NotEmpty(t, file.DiffContent)
				
				// 验证hunks的基本结构
				for _, hunk := range file.Hunks {
					assert.GreaterOrEqual(t, hunk.SourceStartLine, 0)
					assert.GreaterOrEqual(t, hunk.TargetStartLine, 0)
					assert.NotEmpty(t, hunk.DiffContent)
				}
				}
			}
		})
	}
}

func TestNewCodeChange_HunkDetails(t *testing.T) {
	tests := []struct {
		name               string
		diffContent        string
		expectedHunkCount  int
		expectedSourceLine int
		expectedTargetLine int
	}{
		{
			name: "Single hunk with line numbers",
			diffContent: `diff --git a/test.go b/test.go
index 1234567..abcdefg 100644
--- a/test.go
+++ b/test.go
@@ -5,3 +5,4 @@
 package main
 
+import "fmt"
 func main() {
 }`,
			expectedHunkCount:  1,
			expectedSourceLine: 5,
			expectedTargetLine: 5,
		},
		{
			name: "Multiple hunks",
			diffContent: `diff --git a/test.go b/test.go
index 1234567..abcdefg 100644
--- a/test.go
+++ b/test.go
@@ -1,2 +1,3 @@
 package main
+import "fmt"
 func hello() {}
@@ -10,2 +11,3 @@
 func world() {
+    fmt.Println("world")
 }`,
			expectedHunkCount:  2,
			expectedSourceLine: 1,
			expectedTargetLine: 1,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := NewCodeChange(tt.diffContent)
			assert.NoError(t, err)
			assert.NotNil(t, got)
			assert.Len(t, got.Files, 1)

			file := got.Files[0]
			assert.Len(t, file.Hunks, tt.expectedHunkCount)

			// 验证第一个hunk的行号
			if len(file.Hunks) > 0 {
				firstHunk := file.Hunks[0]
				assert.Equal(t, tt.expectedSourceLine, firstHunk.SourceStartLine)
				assert.Equal(t, tt.expectedTargetLine, firstHunk.TargetStartLine)
				assert.GreaterOrEqual(t, firstHunk.SourceEndLine, firstHunk.SourceStartLine)
				assert.GreaterOrEqual(t, firstHunk.TargetEndLine, firstHunk.TargetStartLine)
			}
		})
	}
}

func TestIsSupported(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected bool
	}{
		{name: "Supported Language - Go", input: "go", expected: true},
		{name: "Supported Language - Python", input: "python", expected: true},
		{name: "Unsupported Language - Ruby", input: "ruby", expected: false},
		{name: "Unknown Language String", input: "nonexistent", expected: false},
		{name: "Empty String", input: "", expected: false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equal(t, tt.expected, IsSupported(tt.input))
		})
	}
}

func TestIsLanguageSupported(t *testing.T) {
	tests := []struct {
		name     string
		input    Language
		expected bool
	}{
		{name: "Supported Language - LanguageGo", input: LanguageGo, expected: true},
		{name: "Supported Language - LanguagePython", input: LanguagePython, expected: true},
		{name: "Unsupported Language - LanguageRuby", input: LanguageRuby, expected: false},
		{name: "Unknown Language Constant", input: Language("some_new_lang"), expected: false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equal(t, tt.expected, IsLanguageSupported(tt.input))
		})
	}
}

func TestDetectLanguageFromPath(t *testing.T) {
	tests := []struct {
		name     string
		filePath string
		want     Language
	}{
		// 测试正常文件路径
		{"Go file", "main.go", LanguageGo},
		{"Java file", "Main.java", LanguageJava},
		{"Python file", "script.py", LanguagePython},
		{"JavaScript file", "app.js", LanguageJavaScript},
		{"TypeScript file", "app.ts", LanguageTypeScript},
		{"C file", "main.c", LanguageC},
		{"C++ file (.cpp)", "main.cpp", LanguageCPlusPlus},
		{"C++ file (.cc)", "main.cc", LanguageCPlusPlus},
		{"C++ file (.cxx)", "main.cxx", LanguageCPlusPlus},
		{"C# file", "Program.cs", LanguageCSharp},
		{"Rust file", "main.rs", LanguageRust},
		{"Kotlin file", "Main.kt", LanguageKotlin},
		{"Swift file", "main.swift", LanguageSwift},
		{"PHP file", "index.php", LanguagePHP},
		{"Ruby file", "app.rb", LanguageRuby},
		{"Scala file", "Main.scala", LanguageScala},
		{"R file", "script.r", LanguageR},
		{"Matlab file", "script.m", LanguageMatlab},
		{"Shell file", "script.sh", LanguageShell},
		{"SQL file", "query.sql", LanguageSQL},
		{"HTML file", "index.html", LanguageHTML},
		{"HTML file (.htm)", "index.htm", LanguageHTML},
		{"CSS file", "style.css", LanguageCSS},
		{"XML file", "config.xml", LanguageXML},
		{"JSON file", "package.json", LanguageJSON},
		{"YAML file (.yaml)", "config.yaml", LanguageYAML},
		{"YAML file (.yml)", "config.yml", LanguageYAML},
		{"Markdown file", "README.md", LanguageMarkdown},
		{"Proto file", "api.proto", LanguageProto},
		
		// 测试特殊文件名
		{"Dockerfile", "Dockerfile", LanguageDockerfile},
		{"Dockerfile lowercase", "dockerfile", LanguageDockerfile},
		{"Makefile", "Makefile", LanguageMakefile},
		{"Makefile lowercase", "makefile", LanguageMakefile},
		
		// 测试只有后缀的情况
		{"Go extension only", ".go", LanguageGo},
		{"Java extension only", ".java", LanguageJava},
		{"Python extension only", ".py", LanguagePython},
		{"JavaScript extension only", ".js", LanguageJavaScript},
		{"TypeScript extension only", ".ts", LanguageTypeScript},
		{"C extension only", ".c", LanguageC},
		{"C++ extension only (.cpp)", ".cpp", LanguageCPlusPlus},
		{"C++ extension only (.cc)", ".cc", LanguageCPlusPlus},
		{"C++ extension only (.cxx)", ".cxx", LanguageCPlusPlus},
		{"C# extension only", ".cs", LanguageCSharp},
		{"Rust extension only", ".rs", LanguageRust},
		{"Kotlin extension only", ".kt", LanguageKotlin},
		{"Swift extension only", ".swift", LanguageSwift},
		{"PHP extension only", ".php", LanguagePHP},
		{"Ruby extension only", ".rb", LanguageRuby},
		{"Scala extension only", ".scala", LanguageScala},
		{"R extension only", ".r", LanguageR},
		{"Matlab extension only", ".m", LanguageMatlab},
		{"Shell extension only", ".sh", LanguageShell},
		{"SQL extension only", ".sql", LanguageSQL},
		{"HTML extension only", ".html", LanguageHTML},
		{"HTML extension only (.htm)", ".htm", LanguageHTML},
		{"CSS extension only", ".css", LanguageCSS},
		{"XML extension only", ".xml", LanguageXML},
		{"JSON extension only", ".json", LanguageJSON},
		{"YAML extension only (.yaml)", ".yaml", LanguageYAML},
		{"YAML extension only (.yml)", ".yml", LanguageYAML},
		{"Markdown extension only", ".md", LanguageMarkdown},
		{"Proto extension only", ".proto", LanguageProto},
		
		// 测试大小写不敏感
		{"Go uppercase extension", ".GO", LanguageGo},
		{"Java uppercase extension", ".JAVA", LanguageJava},
		{"Mixed case file", "Main.GO", LanguageGo},
		
		// 测试带有路径的文件
		{"Go file with path", "aaa/bbb/name.go", LanguageGo},
		{"JavaScript file with path", "src/components/App.js", LanguageJavaScript},
		{"TypeScript file with path", "src/utils/helper.ts", LanguageTypeScript},
		{"Python file with path", "scripts/data/process.py", LanguagePython},
		{"Java file with path", "com/example/Main.java", LanguageJava},
		{"C++ file with path", "src/core/engine.cpp", LanguageCPlusPlus},
		{"C file with path", "lib/utils/common.c", LanguageC},
		{"C# file with path", "Models/User.cs", LanguageCSharp},
		{"Rust file with path", "src/main.rs", LanguageRust},
		{"PHP file with path", "public/api/index.php", LanguagePHP},
		{"Ruby file with path", "app/models/user.rb", LanguageRuby},
		{"HTML file with path", "public/templates/index.html", LanguageHTML},
		{"CSS file with path", "assets/styles/main.css", LanguageCSS},
		{"JSON file with path", "config/settings.json", LanguageJSON},
		{"YAML file with path", "deploy/k8s/service.yaml", LanguageYAML},
		{"Markdown file with path", "docs/api/README.md", LanguageMarkdown},
		{"Shell script with path", "scripts/build/deploy.sh", LanguageShell},
		{"SQL file with path", "database/migrations/001_create_users.sql", LanguageSQL},
		{"XML file with path", "config/spring/application.xml", LanguageXML},
		{"Proto file with path", "api/v1/user.proto", LanguageProto},
		{"Dockerfile with path", "docker/app/Dockerfile", LanguageDockerfile},
		{"Makefile with path", "build/Makefile", LanguageMakefile},
		{"Deep nested path", "very/deep/nested/path/to/file.js", LanguageJavaScript},
		{"Windows style path", "src\\components\\App.js", LanguageJavaScript},
		{"Relative path with dots", "../parent/file.py", LanguagePython},
		{"Current dir path", "./current/file.go", LanguageGo},
		{"Absolute path", "/usr/local/bin/script.sh", LanguageShell},
		{"Path with spaces", "my folder/my file.js", LanguageJavaScript},
		{"Path with special chars", "test-dir/file_name.py", LanguagePython},
		{"Unknown file with path", "some/path/unknown.xyz", LanguageUnknown},
		
		// 测试未知类型
		{"Unknown file", "unknown.xyz", LanguageUnknown},
		{"Unknown extension only", ".xyz", LanguageUnknown},
		{"No extension", "README", LanguageUnknown},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := DetectLanguageFromPath(tt.filePath)
			assert.Equal(t, tt.want, got, "DetectLanguageFromPath(%q) = %v, want %v", tt.filePath, got, tt.want)
		})
	}
}

func TestNewCodeChange_HashCalculation(t *testing.T) {
	tests := []struct {
		name        string
		diffContent string
		expectedSha string
		wantErr     bool
	}{
		{
			name: "Simple diff content hash",
			diffContent: `diff --git a/test.go b/test.go
index 1234567..abcdefg 100644
--- a/test.go
+++ b/test.go
@@ -1,3 +1,4 @@
 package main
 
+import "fmt"
 func main() {
 }`,
			expectedSha: func() string {
				content := `diff --git a/test.go b/test.go
index 1234567..abcdefg 100644
--- a/test.go
+++ b/test.go
@@ -1,3 +1,4 @@
 package main
 
+import "fmt"
 func main() {
 }`
				hash := sha256.Sum256([]byte(content))
				return fmt.Sprintf("%x", hash)
			}(),
			wantErr: false,
		},
		{
			name: "Empty diff content hash",
			diffContent: "",
			expectedSha: func() string {
				hash := sha256.Sum256([]byte(""))
				return fmt.Sprintf("%x", hash)
			}(),
			wantErr: false,
		},
		{
			name: "Multi-file diff content hash",
			diffContent: `diff --git a/file1.go b/file1.go
index 1234567..abcdefg 100644
--- a/file1.go
+++ b/file1.go
@@ -1,2 +1,3 @@
 package main
+import "fmt"
 func main() {}
diff --git a/file2.py b/file2.py
index 2345678..bcdefgh 100644
--- a/file2.py
+++ b/file2.py
@@ -1,2 +1,3 @@
 def hello():
+    print("world")
     pass`,
			expectedSha: func() string {
				content := `diff --git a/file1.go b/file1.go
index 1234567..abcdefg 100644
--- a/file1.go
+++ b/file1.go
@@ -1,2 +1,3 @@
 package main
+import "fmt"
 func main() {}
diff --git a/file2.py b/file2.py
index 2345678..bcdefgh 100644
--- a/file2.py
+++ b/file2.py
@@ -1,2 +1,3 @@
 def hello():
+    print("world")
     pass`
				hash := sha256.Sum256([]byte(content))
				return fmt.Sprintf("%x", hash)
			}(),
			wantErr: false,
		},
		{
			name: "Same content should produce same hash",
			diffContent: "test content",
			expectedSha: func() string {
				hash := sha256.Sum256([]byte("test content"))
				return fmt.Sprintf("%x", hash)
			}(),
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := NewCodeChange(tt.diffContent)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, got)
				return
			}

			assert.NoError(t, err)
			assert.NotNil(t, got)
			assert.Equal(t, tt.expectedSha, got.Sha, "Hash value should match expected SHA256")
			assert.NotEmpty(t, got.Sha, "Hash should not be empty")
			assert.Len(t, got.Sha, 64, "SHA256 hash should be 64 characters long")
		})
	}
}

func TestNewCodeChange_HashConsistency(t *testing.T) {
	tests := []struct {
		name        string
		diffContent string
	}{
		{
			name: "Consistency test 1",
			diffContent: `diff --git a/example.go b/example.go
index abc123..def456 100644
--- a/example.go
+++ b/example.go
@@ -1,1 +1,2 @@
 package main
+// added comment`,
		},
		{
			name: "Consistency test 2",
			diffContent: "simple test content",
		},
		{
			name: "Consistency test 3",
			diffContent: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 多次调用应该产生相同的 hash
			result1, err1 := NewCodeChange(tt.diffContent)
			assert.NoError(t, err1)
			assert.NotNil(t, result1)

			result2, err2 := NewCodeChange(tt.diffContent)
			assert.NoError(t, err2)
			assert.NotNil(t, result2)

			assert.Equal(t, result1.Sha, result2.Sha, "Multiple calls with same content should produce same hash")
		})
	}
}

func TestNewCodeChange_HashUniqueness(t *testing.T) {
	tests := []struct {
		name         string
		diffContent1 string
		diffContent2 string
	}{
		{
			name: "Different content should produce different hashes",
			diffContent1: `diff --git a/test1.go b/test1.go
index 123..456 100644
--- a/test1.go
+++ b/test1.go
@@ -1,1 +1,2 @@
 package main
+// comment 1`,
			diffContent2: `diff --git a/test2.go b/test2.go
index 789..abc 100644
--- a/test2.go
+++ b/test2.go
@@ -1,1 +1,2 @@
 package main
+// comment 2`,
		},
		{
			name: "Empty vs non-empty content",
			diffContent1: "",
			diffContent2: "non-empty",
		},
		{
			name: "Similar but different content",
			diffContent1: "test content A",
			diffContent2: "test content B",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result1, err1 := NewCodeChange(tt.diffContent1)
			assert.NoError(t, err1)
			assert.NotNil(t, result1)

			result2, err2 := NewCodeChange(tt.diffContent2)
			assert.NoError(t, err2)
			assert.NotNil(t, result2)

			assert.NotEqual(t, result1.Sha, result2.Sha, "Different content should produce different hashes")
		})
	}
}