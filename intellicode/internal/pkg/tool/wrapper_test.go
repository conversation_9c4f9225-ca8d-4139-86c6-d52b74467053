package tool

import (
	"context"
	"testing"

	"github.com/cloudwego/eino/schema"
	"github.com/getkin/kin-openapi/openapi3"
	"intellicode/internal/pkg/emitter"
	"intellicode/pkg/proto/event"
)

// mockTool 用于测试的模拟工具
type mockTool struct {
	name   string
	result string
	err    error
}

func (m *mockTool) Info(ctx context.Context) (*schema.ToolInfo, error) {
	return &schema.ToolInfo{
		Name: m.name,
		Desc: "Mock tool for testing",
		ParamsOneOf: schema.NewParamsOneOfByOpenAPIV3(&openapi3.Schema{
			Type: openapi3.TypeObject,
		}),
	}, nil
}

func (m *mockTool) InvokableRun(ctx context.Context, argumentsInJSON string, opts ...any) (string, error) {
	return m.result, m.err
}

// mockToolWrapper 用于测试的工具包装器
type mockToolWrapper struct {
	*BaseTool
	originalTool *mockTool
}

func newMockToolWrapper(name, result string, err error) *mockToolWrapper {
	originalTool := &mockTool{
		name:   name,
		result: result,
		err:    err,
	}

	wrapper := &mockToolWrapper{
		BaseTool:     NewBaseTool(name, "Mock tool", &openapi3.Schema{Type: openapi3.TypeObject}),
		originalTool: originalTool,
	}

	return wrapper
}

func (mtw *mockToolWrapper) InvokableRun(ctx context.Context, argumentsInJSON string, opts ...any) (string, error) {
	return mtw.WrapInvokableRun(ctx, argumentsInJSON, func(ctx context.Context, input string) (string, error) {
		return mtw.originalTool.InvokableRun(ctx, input, opts...)
	})
}

func TestBaseTool(t *testing.T) {
	baseTool := NewBaseTool("TestTool", "Test description", &openapi3.Schema{Type: openapi3.TypeObject})

	// 测试基本属性
	if baseTool.GetName() != "TestTool" {
		t.Errorf("Expected name 'TestTool', got '%s'", baseTool.GetName())
	}

	if !baseTool.ShouldShowInUI() {
		t.Error("Expected ShouldShowInUI to be true by default")
	}

	// 测试设置 UI 显示
	baseTool.SetShowInUI(false)
	if baseTool.ShouldShowInUI() {
		t.Error("Expected ShouldShowInUI to be false after setting")
	}

	// 测试 Info 方法
	ctx := context.Background()
	info, err := baseTool.Info(ctx)
	if err != nil {
		t.Errorf("Unexpected error: %v", err)
	}

	if info.Name != "TestTool" {
		t.Errorf("Expected info name 'TestTool', got '%s'", info.Name)
	}
}

func TestToolWrapperWithEvents(t *testing.T) {
	// 创建事件发送器
	emitter := emitter.NewEmitter()
	defer emitter.Close()

	// 创建模拟工具包装器
	wrapper := newMockToolWrapper("TestTool", "test result", nil)
	wrapper.SetEventEmitter(emitter)

	ctx := context.Background()

	// 异步执行工具
	go func() {
		_, err := wrapper.InvokableRun(ctx, `{"test": "input"}`)
		if err != nil {
			t.Errorf("Unexpected error: %v", err)
		}
	}()

	// 验证事件
	events := make([]*event.Event, 0)

	// 收集所有事件
	for e := range emitter.Events() {
		events = append(events, e)
		if len(events) == 2 { // 期望收到 start 和 end 事件
			break
		}
	}

	if len(events) != 2 {
		t.Fatalf("Expected 2 events, got %d", len(events))
	}

	// 验证 start 事件
	startEvent := events[0]
	if startEvent.Type != event.EventType_EVENT_TYPE_TOOL_START {
		t.Errorf("Expected first event type %s, got %s", event.EventType_EVENT_TYPE_TOOL_START, startEvent.Type)
	}

	startData := startEvent.GetToolStart()
	if startData == nil {
		t.Error("Expected ToolStartData for start event")
	}

	if startData.Name != "TestTool" {
		t.Errorf("Expected start event tool name 'TestTool', got '%s'", startData.Name)
	}

	// 验证 end 事件
	endEvent := events[1]
	if endEvent.Type != event.EventType_EVENT_TYPE_TOOL_END {
		t.Errorf("Expected second event type %s, got %s", event.EventType_EVENT_TYPE_TOOL_END, endEvent.Type)
	}

	endData := endEvent.GetToolEnd()
	if endData == nil {
		t.Error("Expected ToolEndData for end event")
	}

	if endData.Name != "TestTool" {
		t.Errorf("Expected end event tool name 'TestTool', got '%s'", endData.Name)
	}

	if endData.Status != event.ToolStatus_TOOL_STATUS_SUCCESS {
		t.Errorf("Expected end event status TOOL_STATUS_SUCCESS, got %s", endData.Status)
	}
}

func TestToolWrapperWithError(t *testing.T) {
	// 创建事件发送器
	emitter := emitter.NewEmitter()
	defer emitter.Close()

	// 创建会返回错误的模拟工具包装器
	wrapper := newMockToolWrapper("ErrorTool", "", NewToolErrorf("test error"))
	wrapper.SetEventEmitter(emitter)

	ctx := context.Background()

	// 异步执行工具
	go func() {
		_, err := wrapper.InvokableRun(ctx, `{"test": "input"}`)
		if err == nil {
			t.Error("Expected error but got none")
		}
	}()

	// 验证错误事件
	events := make([]*event.Event, 0)

	// 收集所有事件
	for e := range emitter.Events() {
		events = append(events, e)
		if len(events) == 2 { // 期望收到 start 和 end 事件
			break
		}
	}

	if len(events) != 2 {
		t.Fatalf("Expected 2 events, got %d", len(events))
	}

	// 验证 end 事件包含错误状态
	endEvent := events[1]
	endData := endEvent.GetToolEnd()
	if endData == nil {
		t.Error("Expected ToolEndData for end event")
	}

	if endData.Status != event.ToolStatus_TOOL_STATUS_ERROR {
		t.Errorf("Expected end event status TOOL_STATUS_ERROR, got %s", endData.Status)
	}
}

func TestToolWrapperHiddenFromUI(t *testing.T) {
	// 创建事件发送器
	emitter := emitter.NewEmitter()
	defer emitter.Close()

	// 创建隐藏的工具包装器
	wrapper := newMockToolWrapper("HiddenTool", "result", nil)
	wrapper.SetShowInUI(false) // 隐藏工具
	wrapper.SetEventEmitter(emitter)

	ctx := context.Background()

	// 异步执行工具
	go func() {
		_, err := wrapper.InvokableRun(ctx, `{"test": "input"}`)
		if err != nil {
			t.Errorf("Unexpected error: %v", err)
		}
		emitter.Close() // 手动关闭以结束事件循环
	}()

	// 不应该收到任何事件
	select {
	case <-emitter.Events():
		t.Error("Should not receive events for hidden tools")
	default:
		// 正确：没有收到事件
	}
}

