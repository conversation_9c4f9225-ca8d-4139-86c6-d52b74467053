package tool

import (
	"context"
	"fmt"
	"strings"

	"github.com/cloudwego/eino/components/tool"
	"github.com/cloudwego/eino/schema"
)

// ReadToolWrapper Read 工具的包装器实现
type ReadToolWrapper struct {
	*BaseTool
	originalTool tool.InvokableTool
}

// NewReadToolWrapper 创建 Read 工具包装器
func NewReadToolWrapper(projectDir string) *ReadToolWrapper {
	// 创建原始工具实例
	originalTool := NewReadTool(projectDir)

	// 创建包装器
	wrapper := &ReadToolWrapper{
		BaseTool:     NewBaseTool("Read", "Read file content", nil),
		originalTool: originalTool,
	}

	return wrapper
}

// Info 返回工具信息
func (rtw *ReadToolWrapper) Info(ctx context.Context) (*schema.ToolInfo, error) {
	return rtw.originalTool.Info(ctx)
}

// InvokableRun 实现 InvokableTool 接口
func (rtw *ReadToolWrapper) InvokableRun(ctx context.Context, argumentsInJSON string, opts ...tool.Option) (string, error) {
	return rtw.WrapInvokableRun(ctx, argumentsInJSON, func(ctx context.Context, input string) (string, error) {
		return rtw.originalTool.InvokableRun(ctx, input, opts...)
	})
}

// FormatEvent 格式化事件输出，显示文件读取统计
func (rtw *ReadToolWrapper) FormatEvent(input, result string, err error) string {
	if err != nil {
		return fmt.Sprintf("Failed to read file: %s", err.Error())
	}

	// 统计行数用于 UI 展示
	lines := strings.Split(result, "\n")
	lineCount := len(lines)
	if lineCount > 0 && lines[lineCount-1] == "" {
		lineCount-- // 去除末尾空行
	}

	return fmt.Sprintf("Read file, total %d lines", lineCount)
}