package tool

import (
	"context"
	"errors"
	"fmt"
	"strings"
)

// TODO: move to error_handler.go
// ToolErrorHandler 用于 wrap eino tool 以实现统一的 tool 错误处理。
// 默认情况下，tool 返回 error 时，eino 引擎会退出，中断整个流程的执行。（eino 将 error 视为异常）
// 在 Agent 中，我们会期望 tool 执行时出现的错误能够返回给 LLM，而不是直接中断流程。
// 因此，我们需要在 Agent 中对 tool 执行时出现的 error 进行 wrap，以实现统一的错误处理。
func ToolErrorHandler(_ context.Context, err error) (string, error) {
	var toolErr *ToolError
	if err == nil {
		return "", nil
	}

	if errors.As(err, &toolErr) {
		// ToolError 则视为需要返回给 LLM 的错误信息
		// Notice: 在 eino 引擎中会对 ToolError 进行 wrap，添加额外的错误信息。由于这里的错误信息是给 LLM 的，因此需要去掉额外的错误信息。
		return toolErr.Error(), nil
	} else if strings.Contains(err.Error(), "failed to unmarshal arguments in json") {
		// FYI: https://github.com/cloudwego/eino/blob/aaa9e1b579f6879aaf6499e37320eb77e8d778b6/components/tool/utils/invokable_func.go#L153
		// 由于 eino 没有明确的返回 error type，所以这里只能通过 error message 字符串匹配判断。
		// 这个错误触发都是因为 LLM tool call 给的参数不合法导致的。
		return "Invalid input format, please check your input and try again.", nil
	} else {
		// 非 ToolError 则视为异常，直接返回（流程中断）
		return "", err
	}
}

type ToolError struct {
	message string
}

func (e *ToolError) Error() string {
	return e.message
}

// NewToolErrorf 用于记录返回给 LLM 的错误信息。（注意输入 llm-friendly 的信息）
// ToolError 的使用场景中不会需要 wrap error 或者堆栈信息。如果需要记录就主动使用 log 或者 tracing 记录。
func NewToolErrorf(format string, args ...interface{}) *ToolError {
	return &ToolError{message: fmt.Sprintf(format, args...)}
}
