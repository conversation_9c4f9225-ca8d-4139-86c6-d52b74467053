package tool

import (
	"context"
	"fmt"
	"os"
	"strings"

	"github.com/cloudwego/eino/components/tool"
	"github.com/cloudwego/eino/components/tool/utils"
	"github.com/cloudwego/eino/schema"
	"github.com/getkin/kin-openapi/openapi3"
	"github.com/pkg/errors"
)

// ReadRequest 定义Read工具的请求参数
type ReadRequest struct {
	// Path 文件路径，相对于项目根目录
	Path string `json:"path"`
	// Offset 起始行号，从1开始，可选参数
	Offset *int `json:"offset,omitempty"`
	// Limit 读取行数，可选参数
	Limit *int `json:"limit,omitempty"`
}

// ReadResponse 定义Read工具的响应
type ReadResponse struct {
	// Content 文件或目录的内容
	Content string `json:"content"`
}

// readTool 文件读取工具
type readTool struct {
	pathHelper *PathHelper
}

const (
	// maxReadLines 文件读取的最大行数限制
	maxReadLines = 1000

	readToolName = "Read"
	readToolDesc = `从本地文件系统读取文件。你可以使用此工具直接访问任何文件。
注意：通过这个工具获得的是当前最新文件内容。

用法：
  - path 参数必须是绝对路径，不能是相对路径
  - 默认情况下，从文件开头最多读取1000行
  - 任何超过1000行的内容将被截断
  - offset, limit 参数是可选的（对于大文件特别有用），但推荐不指定这些参数以读取整个文件

返回值：
  - 格式为：<file path="xxx"><content>xxx</content></file>
  - <content>中的结果以 cat -n 格式返回，行号从1开始
`
)

// NewReadTool 创建一个新的ReadTool实例
func NewReadTool(projectDir string) tool.InvokableTool {
	readTool := &readTool{
		pathHelper: NewPathHelper(projectDir),
	}

	toolInfo := &schema.ToolInfo{
		Name: readToolName,
		Desc: readToolDesc,
		ParamsOneOf: schema.NewParamsOneOfByOpenAPIV3(&openapi3.Schema{
			Type: openapi3.TypeObject,
			Properties: map[string]*openapi3.SchemaRef{
				"path": {
					Value: &openapi3.Schema{
						Type:        openapi3.TypeString,
						Description: "当前项目范围内的文件路径，必须使用绝对路径",
					},
				},
				"offset": {
					Value: &openapi3.Schema{
						Type:        openapi3.TypeInteger,
						Description: "起始读取的行号，仅当文件过大时提供该参数",
					},
				},
				"limit": {
					Value: &openapi3.Schema{
						Type:        openapi3.TypeInteger,
						Description: "读取的行数，仅当文件过大时提供该参数。",
					},
				},
			},
			Required: []string{"path"},
		}),
	}
	readInvokableTool := utils.NewTool(toolInfo, readTool.processRead)
	return readInvokableTool
}

// readFile 读取文件内容
func (r *readTool) readFile(path string, offset *int, limit *int) (string, error) {
	if !r.pathHelper.IsFile(path) {
		return "", NewToolErrorf("File not found: %s", path)
	}

	if !r.pathHelper.HasReadPermission(path) {
		return "", NewToolErrorf("Permission denied: %s", path)
	}

	lines, err := r.readFileLines(path)
	if err != nil {
		return "", NewToolErrorf("Failed to read file: %s err:%s", path, err)
	}

	var selectedLines []string
	var startLineNum int

	if offset == nil && limit == nil {
		// 读取整个文件，限制最大行数
		if len(lines) > maxReadLines {
			lines = lines[:maxReadLines]
		}
		selectedLines = lines
		startLineNum = 1
	} else {
		// 提取指定范围的行
		var err error
		selectedLines, startLineNum, err = r.extractLines(lines, offset, limit)
		if err != nil {
			return "", NewToolErrorf("Failed to read file: %s, err:%s", path, err)
		}
	}

	// 统一在这里添加行号格式化
	return r.formatWithLineNumbers(selectedLines, startLineNum), nil
}

// readFileLines 读取文件所有行
func (r *readTool) readFileLines(path string) ([]string, error) {
	content, err := os.ReadFile(path)
	if err != nil {
		return nil, err
	}

	// 按行分割，保留换行符
	lines := strings.Split(string(content), "\n")
	// 为每行添加换行符（除了最后一行如果原本没有）
	for i := 0; i < len(lines)-1; i++ {
		lines[i] += "\n"
	}
	// 如果原文件以换行符结尾，最后一行也要加换行符
	if len(content) > 0 && content[len(content)-1] == '\n' {
		lines[len(lines)-1] += "\n"
	}

	return lines, nil
}

// extractLines 根据 offset 和 limit 提取指定行
func (r *readTool) extractLines(lines []string, offset *int, limit *int) ([]string, int, error) {
	totalLines := len(lines)

	// 设置默认值
	startLine := 1
	if offset != nil {
		startLine = *offset
	}

	var maxLines int
	if limit != nil {
		maxLines = *limit
	} else {
		maxLines = maxReadLines
	}

	// 验证参数
	if startLine < 1 {
		return nil, 0, errors.Errorf("Invalid offset: %d, must be >= 1", startLine)
	}

	if maxLines < 1 {
		return nil, 0, errors.Errorf("Invalid limit: %d, must be >= 1", maxLines)
	}

	if startLine > totalLines {
		return nil, 0, errors.Errorf("Offset %d exceeds total lines %d", startLine, totalLines)
	}

	// 计算结束位置
	endLine := startLine + maxLines - 1
	if endLine > totalLines {
		endLine = totalLines
	}

	// 提取指定行
	selected := lines[startLine-1 : endLine]

	// 限制最大行数
	if len(selected) > maxReadLines {
		selected = selected[:maxReadLines]
	}

	if len(selected) == 0 {
		return nil, 0, errors.Errorf("No content found for offset=%d, limit=%d", startLine, maxLines)
	}

	// 返回提取的行和起始行号
	return selected, startLine, nil
}

// formatWithLineNumbers 为内容添加行号，类似 cat -n 的效果
func (r *readTool) formatWithLineNumbers(lines []string, startLineNum int) string {
	var result strings.Builder

	for i, line := range lines {
		fmt.Fprintf(&result, "%6d %s", startLineNum+i, line)
	}

	return result.String()
}

// processRead 处理Read工具请求
func (r *readTool) processRead(ctx context.Context, req *ReadRequest) (*ReadResponse, error) {
	// 获取绝对路径
	filePath := r.pathHelper.GetAbsolutePath(req.Path)

	// 检查路径是否存在
	stat, err := os.Stat(filePath)
	if err != nil {
		if os.IsNotExist(err) {
			return nil, NewToolErrorf("Path not found: %s", req.Path)
		}
		return nil, errors.Wrapf(err, "Failed to access path: %s", req.Path)
	}

	var content string
	if stat.IsDir() {
		return nil, NewToolErrorf("Path: %s is a directory, support file only", req.Path)
	}

	content, err = r.readFile(filePath, req.Offset, req.Limit)
	if err != nil {
		return nil, err
	}

	return &ReadResponse{
		Content: fmt.Sprintf("<file path=\"%s\">\n<content>\n%s\n</content>\n</file>", filePath, content),
	}, nil
}
