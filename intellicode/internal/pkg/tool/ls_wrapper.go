package tool

import (
	"context"
	"fmt"
	"strings"

	"github.com/cloudwego/eino/components/tool"
	"github.com/cloudwego/eino/schema"
)

// LSTreeWrapper LSTree 工具的包装器实现
type LSTreeWrapper struct {
	*BaseTool
	originalTool tool.InvokableTool
}

// NewLSTreeWrapper 创建 LSTree 工具包装器
func NewLSTreeWrapper(projectDir string) *LSTreeWrapper {
	// 创建原始工具实例
	originalTool := NewLSTool(projectDir)

	// 创建包装器
	wrapper := &LSTreeWrapper{
		BaseTool:     NewBaseTool("LsTree", "List directory structure", nil),
		originalTool: originalTool,
	}

	return wrapper
}

// Info 返回工具信息
func (ltw *LSTreeWrapper) Info(ctx context.Context) (*schema.ToolInfo, error) {
	return ltw.originalTool.Info(ctx)
}

// InvokableRun 实现 InvokableTool 接口
func (ltw *LSTreeWrapper) InvokableRun(ctx context.Context, argumentsInJSON string, opts ...tool.Option) (string, error) {
	return ltw.WrapInvokableRun(ctx, argumentsInJSON, func(ctx context.Context, input string) (string, error) {
		return ltw.originalTool.InvokableRun(ctx, input, opts...)
	})
}

// FormatEvent 格式化事件输出，显示文件和目录统计
func (ltw *LSTreeWrapper) FormatEvent(input, result string, err error) string {
	if err != nil {
		return fmt.Sprintf("Failed to list directory: %s", err.Error())
	}

	if strings.TrimSpace(result) == "" {
		return "Empty directory"
	}

	// 统计文件和目录数量
	lines := strings.Split(strings.TrimSpace(result), "\n")
	fileCount := 0
	dirCount := 0

	for _, line := range lines {
		if strings.Contains(line, "/") {
			dirCount++
		} else {
			fileCount++
		}
	}

	return fmt.Sprintf("Listed %d files and %d directories", fileCount, dirCount)
}