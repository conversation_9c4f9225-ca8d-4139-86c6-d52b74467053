package tool

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"strings"

	"github.com/cloudwego/eino/components/tool"
	"github.com/cloudwego/eino/components/tool/utils"
	"github.com/cloudwego/eino/schema"
	"github.com/getkin/kin-openapi/openapi3"
	"github.com/pkg/errors"
)

const (
	// 目录列表的最大条目数
	MAX_LS_FILES = 200

	// 超过最大条目数时的提示信息
	TRUNCATED_MESSAGE = "路径 %s 中有超过 %d 个文件。请通过 path 参数指定更加具体的路径，以下是前 %d 个文件和目录：\n\n"

	// 工具名称和描述
	lsToolName = "LsTree"
	lsToolDesc = `列出指定路径（path 参数）下的文件和子目录。

用法：
- path 参数必须是绝对路径，不能是相对路径
- 默认情况下会递归列出整个目录树，显示最多%d个条目
- 你可以使用 ignore 参数来指定要忽略的 glob pattern 数组，这些 pattern 将被排除
- 注意：如果你知道要搜索的目录，通常应该优先使用 RipGrep 工具。
`
)

// LSRequest 定义LS工具的请求参数
type LSRequest struct {
	// Path 目录路径，必须是绝对路径
	Path string `json:"path"`
	// Ignore 忽略的 glob pattern 数组
	Ignore []string `json:"ignore,omitempty"`
}

// LSResponse 定义LS工具的响应
type LSResponse struct {
	// Tree 目录树结构
	Tree string `json:"tree"`
}

// lsTool 目录列表工具
type lsTool struct {
	pathHelper *PathHelper
}

// NewLSTool 创建一个新的LSTool实例
func NewLSTool(projectDir string) tool.InvokableTool {
	lsTool := &lsTool{
		pathHelper: NewPathHelper(projectDir),
	}

	toolInfo := &schema.ToolInfo{
		Name: lsToolName,
		Desc: fmt.Sprintf(lsToolDesc, MAX_LS_FILES),
		ParamsOneOf: schema.NewParamsOneOfByOpenAPIV3(&openapi3.Schema{
			Type: openapi3.TypeObject,
			Properties: map[string]*openapi3.SchemaRef{
				"path": {
					Value: &openapi3.Schema{
						Type:        openapi3.TypeString,
						Description: "要列出内容的目录路径，必须使用绝对路径",
					},
				},
				"ignore": {
					Value: &openapi3.Schema{
						Type: openapi3.TypeArray,
						Items: &openapi3.SchemaRef{
							Value: &openapi3.Schema{
								Type: openapi3.TypeString,
							},
						},
						Description: "要忽略的 glob pattern 数组，这些 pattern 将被排除，例如：['*.log', 'temp/']",
					},
				},
			},
			Required: []string{"path"},
		}),
	}
	lsInvokableTool := utils.NewTool(toolInfo, lsTool.processLS)
	return lsInvokableTool
}

// TreeNode 表示文件树中的一个节点
type TreeNode struct {
	Name     string
	Path     string
	Type     string // "file" 或 "directory"
	Children []*TreeNode
}

// processLS 处理LS工具请求
func (l *lsTool) processLS(ctx context.Context, req *LSRequest) (*LSResponse, error) {
	dirPath := l.pathHelper.GetAbsolutePath(req.Path)

	stat, err := os.Stat(dirPath)
	if err != nil {
		if os.IsNotExist(err) {
			return nil, NewToolErrorf("Path not found: %s", req.Path)
		}
		return nil, errors.Wrapf(err, "Failed to access path: %s", req.Path)
	}

	if !stat.IsDir() {
		return nil, NewToolErrorf("Path: %s is not a directory", req.Path)
	}

	if !l.pathHelper.HasReadPermission(dirPath) {
		return nil, NewToolErrorf("Permission denied: %s", req.Path)
	}

	// 列出目录内容，使用ripgrep尊重.gitignore规则
	results, err := l.listDirectoryWithGitIgnore(dirPath, req.Ignore)
	if err != nil {
		return nil, errors.Wrapf(err, "Failed to list directory: %s", req.Path)
	}

	sort.Strings(results)

	// 创建树结构
	tree := l.createFileTree(results)
	userTree := l.printTree(tree, 0, "")

	// 根据结果数量决定是否显示截断提示
	var finalOutput string
	if len(results) < MAX_LS_FILES {
		finalOutput = userTree
	} else {
		finalOutput = fmt.Sprintf(TRUNCATED_MESSAGE, dirPath, MAX_LS_FILES, MAX_LS_FILES) + userTree
	}

	return &LSResponse{
		Tree: finalOutput,
	}, nil
}

// listDirectoryWithGitIgnore 使用ripgrep列出目录内容，遵循.gitignore规则
func (l *lsTool) listDirectoryWithGitIgnore(dirPath string, ignorePatterns []string) ([]string, error) {
	limit := MAX_LS_FILES

	// 使用ripgrep的ListAllContentFiles获取文件列表
	files, err := ListAllContentFilesWithIgnore(dirPath, &limit, ignorePatterns)
	if err != nil {
		return nil, err
	}

	// 获取相对路径结果
	cwd := l.pathHelper.GetCurrentWorkingDirectory()
	results := make([]string, 0, len(files))

	// 处理文件路径，提取相对路径
	for _, file := range files {
		// 如果文件路径是绝对路径，则转换为相对于工作目录的路径
		var relPath string
		if filepath.IsAbs(file) {
			var err error
			relPath, err = filepath.Rel(cwd, file)
			if err != nil {
				continue // 跳过无法转换的路径
			}
		} else {
			// 如果已经是相对路径，但可能是相对于dirPath的
			relPath = file
		}

		// 确保路径确实是相对于要列出的目录的
		if dirPath != cwd {
			fullPath := filepath.Join(dirPath, relPath)
			relToDir, err := filepath.Rel(dirPath, fullPath)
			if err == nil {
				relPath = relToDir
			}
		}

		// 添加目录的尾部斜杠
		if l.pathHelper.IsDirectory(filepath.Join(dirPath, relPath)) {
			relPath += string(filepath.Separator)
		}

		results = append(results, relPath)
	}

	// 确保包含目录结构
	l.ensureDirectoryStructure(&results)
	return results, nil
}

// ensureDirectoryStructure 确保结果中包含完整的目录结构
func (l *lsTool) ensureDirectoryStructure(results *[]string) {
	dirMap := make(map[string]bool)

	// 添加所有已有路径
	for _, path := range *results {
		dirMap[path] = true
	}

	// 为每个文件路径创建所有父目录路径
	var additionalPaths []string
	for _, path := range *results {
		dir := filepath.Dir(path)
		for dir != "." && dir != "/" && !dirMap[dir+string(filepath.Separator)] {
			dirMap[dir+string(filepath.Separator)] = true
			additionalPaths = append(additionalPaths, dir+string(filepath.Separator))
			dir = filepath.Dir(dir)
		}
	}

	// 添加额外的目录路径
	*results = append(*results, additionalPaths...)
}

// createFileTree 从路径列表构建文件树结构
func (l *lsTool) createFileTree(sortedPaths []string) []*TreeNode {
	root := []*TreeNode{}

	for _, p := range sortedPaths {
		parts := strings.Split(p, string(filepath.Separator))
		currentLevel := &root
		currentPath := ""

		for i, part := range parts {
			if part == "" && i != len(parts)-1 { // 处理目录的尾部斜杠
				continue
			}

			if currentPath == "" {
				currentPath = part
			} else {
				currentPath = filepath.Join(currentPath, part)
			}

			isLastPart := (i == len(parts)-1) || (i == len(parts)-2 && parts[len(parts)-1] == "")
			existingNode := l.findNode(*currentLevel, part)

			if existingNode != nil {
				currentLevel = &existingNode.Children
			} else {
				newNode := &TreeNode{
					Name: part,
					Path: currentPath,
					Type: "file",
				}
				if !isLastPart || (isLastPart && strings.HasSuffix(p, string(filepath.Separator))) {
					newNode.Type = "directory"
					newNode.Children = []*TreeNode{}
				}

				*currentLevel = append(*currentLevel, newNode)
				currentLevel = &newNode.Children
			}
		}
	}

	return root
}

// findNode 在节点列表中查找指定名称的节点
func (l *lsTool) findNode(nodes []*TreeNode, name string) *TreeNode {
	for _, node := range nodes {
		if node.Name == name {
			return node
		}
	}
	return nil
}

// printTree 将树结构格式化为可读的字符串
func (l *lsTool) printTree(tree []*TreeNode, level int, prefix string) string {
	var result strings.Builder

	// 在根级别添加绝对路径
	if level == 0 {
		cwd := l.pathHelper.GetCurrentWorkingDirectory()
		result.WriteString(fmt.Sprintf("- %s%s\n", cwd, string(filepath.Separator)))
		prefix = "  "
	}

	for _, node := range tree {
		result.WriteString(fmt.Sprintf("%s- %s%s\n", prefix, node.Name, func() string {
			if node.Type == "directory" {
				return string(filepath.Separator)
			}
			return ""
		}()))

		// 递归打印子节点
		if len(node.Children) > 0 {
			result.WriteString(l.printTree(node.Children, level+1, prefix+"  "))
		}
	}

	return result.String()
}
