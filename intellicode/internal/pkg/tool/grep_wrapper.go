package tool

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"github.com/cloudwego/eino/components/tool"
	"github.com/cloudwego/eino/schema"
)

// RipGrepWrapper RipGrep 工具的包装器实现
type RipGrepWrapper struct {
	*BaseTool
	originalTool tool.InvokableTool
}

// NewRipGrepWrapper 创建 RipGrep 工具包装器
func NewRipGrepWrapper(projectDir string) *RipGrepWrapper {
	// 创建原始工具实例
	originalTool := NewGrepTool(projectDir)

	// 创建包装器
	wrapper := &RipGrepWrapper{
		BaseTool:     NewBaseTool("RipGrep", "Search files with ripgrep", nil),
		originalTool: originalTool,
	}

	return wrapper
}

// Info 返回工具信息
func (rgw *RipGrepWrapper) Info(ctx context.Context) (*schema.ToolInfo, error) {
	return rgw.originalTool.Info(ctx)
}

// InvokableRun 实现 InvokableTool 接口
func (rgw *RipGrepWrapper) InvokableRun(ctx context.Context, argumentsInJSON string, opts ...tool.Option) (string, error) {
	return rgw.WrapInvokableRun(ctx, argumentsInJSON, func(ctx context.Context, input string) (string, error) {
		return rgw.originalTool.InvokableRun(ctx, input, opts...)
	})
}

// FormatEvent 格式化事件输出，根据不同的输出模式显示搜索统计
func (rgw *RipGrepWrapper) FormatEvent(input, result string, err error) string {
	if err != nil {
		return fmt.Sprintf("Search failed: %s", err.Error())
	}

	// 解析输入参数以确定输出模式
	var params struct {
		OutputMode string `json:"output_mode"`
	}
	json.Unmarshal([]byte(input), &params)

	switch params.OutputMode {
	case "files_with_matches":
		if strings.TrimSpace(result) == "" {
			return "No files found with matches"
		}
		fileCount := len(strings.Split(strings.TrimSpace(result), "\n"))
		return fmt.Sprintf("Found matches in %d files", fileCount)
	case "count":
		return fmt.Sprintf("Match count: %s", strings.TrimSpace(result))
	case "content":
		if strings.TrimSpace(result) == "" {
			return "No matching lines found"
		}
		lineCount := len(strings.Split(strings.TrimSpace(result), "\n"))
		return fmt.Sprintf("Found %d matching lines", lineCount)
	default:
		return "Search completed"
	}
}