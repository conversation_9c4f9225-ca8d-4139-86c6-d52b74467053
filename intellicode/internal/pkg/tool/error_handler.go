package tool

import (
	"context"

	"github.com/cloudwego/eino/components/tool"
	"github.com/cloudwego/eino/schema"
)

type ErrorHandler func(context.Context, error) (string, error)

// WrapToolWithErrorHandler 使用自定义错误处理包装任意 BaseTool。
// 此函数会检测工具类型（InvokableTool、StreamableTool 或两者都是）
// 并应用相应的错误处理包装器。
// 当被包装的工具返回错误时，错误处理函数 'h' 将被调用
// 以实现一个 AOP 风格的错误处理。
//
// 参数：
//   - t: 需要被包装的原始 BaseTool
//   - h: 实际处理错误的函数，作为自定义错误处理的 hook 函数。
//
// 返回：
//   - 一个根据其功能进行内部错误处理的包装后的 BaseTool

func WrapToolWithErrorHandler(t tool.BaseTool, h ErrorHandler) tool.BaseTool {
	ih := &infoHelper{info: t.Info}
	var s tool.StreamableTool
	if st, ok := t.(tool.StreamableTool); ok {
		s = st
	}
	if it, ok := t.(tool.InvokableTool); ok {
		if s == nil {
			return WrapInvokableToolWithErrorHandler(it, h)
		} else {
			return &combinedErrorWrapper{
				infoHelper: ih,
				errorHelper: &errorHelper{
					i: it.InvokableRun,
					h: h,
				},
				streamErrorHelper: &streamErrorHelper{
					s: s.StreamableRun,
					h: h,
				},
			}
		}
	}
	if s != nil {
		return WrapStreamableToolWithErrorHandler(s, h)
	}
	return t
}

func WrapInvokableToolWithErrorHandler(t tool.InvokableTool, h ErrorHandler) tool.InvokableTool {
	return &errorWrapper{
		infoHelper: &infoHelper{info: t.Info},
		errorHelper: &errorHelper{
			i: t.InvokableRun,
			h: h,
		},
	}
}

func WrapStreamableToolWithErrorHandler(t tool.StreamableTool, h ErrorHandler) tool.StreamableTool {
	return &streamErrorWrapper{
		infoHelper: &infoHelper{info: t.Info},
		streamErrorHelper: &streamErrorHelper{
			s: t.StreamableRun,
			h: h,
		},
	}
}

type errorWrapper struct {
	*infoHelper
	*errorHelper
}

type streamErrorWrapper struct {
	*infoHelper
	*streamErrorHelper
}

type combinedErrorWrapper struct {
	*infoHelper
	*errorHelper
	*streamErrorHelper
}

type infoHelper struct {
	info func(ctx context.Context) (*schema.ToolInfo, error)
}

func (i *infoHelper) Info(ctx context.Context) (*schema.ToolInfo, error) {
	return i.info(ctx)
}

type errorHelper struct {
	i func(ctx context.Context, argumentsInJSON string, opts ...tool.Option) (string, error)
	h ErrorHandler
}

func (s *errorHelper) InvokableRun(ctx context.Context, argumentsInJSON string, opts ...tool.Option) (string, error) {
	result, err := s.i(ctx, argumentsInJSON, opts...)
	if err != nil {
		return s.h(ctx, err)
	}
	return result, nil
}

type streamErrorHelper struct {
	s func(ctx context.Context, argumentsInJSON string, opts ...tool.Option) (*schema.StreamReader[string], error)
	h ErrorHandler
}

func (s *streamErrorHelper) StreamableRun(ctx context.Context, argumentsInJSON string, opts ...tool.Option) (*schema.StreamReader[string], error) {
	result, err := s.s(ctx, argumentsInJSON, opts...)
	if err != nil {
		str, handlerErr := s.h(ctx, err)
		return schema.StreamReaderFromArray([]string{str}), handlerErr
	}
	return result, nil
}
