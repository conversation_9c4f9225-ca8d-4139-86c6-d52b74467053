package tool

import (
	"os"
	"path/filepath"
)

// PathHelper 提供文件路径相关的辅助函数
type PathHelper struct {
	// projectDir 项目根目录，用于将相对路径转换为绝对路径
	projectDir string
}

// NewPathHelper 创建一个新的PathHelper实例
func NewPathHelper(projectDir string) *PathHelper {
	return &PathHelper{
		projectDir: projectDir,
	}
}

// GetAbsolutePath 将路径转换为绝对路径
// 如果输入路径已经是绝对路径，则直接返回
// 如果输入路径是相对路径，则相对于项目根目录进行解析
func (p *PathHelper) GetAbsolutePath(path string) string {
	if filepath.IsAbs(path) {
		return path
	}
	return filepath.Join(p.projectDir, path)
}

// GetCurrentWorkingDirectory 获取当前工作目录
func (p *PathHelper) GetCurrentWorkingDirectory() string {
	cwd, _ := os.Getwd()
	return cwd
}

// IsDirectory 检查路径是否为目录
func (p *PathHelper) IsDirectory(path string) bool {
	stat, err := os.Stat(path)
	return err == nil && stat.IsDir()
}

// IsFile 检查路径是否为文件
func (p *PathHelper) IsFile(path string) bool {
	stat, err := os.Stat(path)
	return err == nil && !stat.IsDir()
}

// HasReadPermission 检查是否有读取权限
func (p *PathHelper) HasReadPermission(path string) bool {
	f, err := os.Open(path)
	if err != nil {
		return false
	}
	f.Close()
	return true
}