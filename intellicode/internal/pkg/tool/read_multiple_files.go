package tool

import (
	"context"
	"fmt"
	"os"
	"strings"

	"github.com/cloudwego/eino/components/tool"
	"github.com/cloudwego/eino/components/tool/utils"
	"github.com/cloudwego/eino/schema"
	"github.com/getkin/kin-openapi/openapi3"
)

// FileReadSpec 定义单个文件的读取规范
type FileReadSpec struct {
	Path   string `json:"path"`
	Offset *int   `json:"offset,omitempty"`
	Limit  *int   `json:"limit,omitempty"`
}

// ReadMultipleFilesRequest 定义ReadMultipleFiles工具的请求参数
type ReadMultipleFilesRequest struct {
	// Files 文件读取规范列表
	Files []FileReadSpec `json:"files"`
}

// ReadMultipleFilesResponse 定义ReadMultipleFiles工具的响应
type ReadMultipleFilesResponse struct {
	// Content 所有文件内容的组合
	Content string `json:"content"`
}

// readMultipleFilesTool 多文件读取工具
type readMultipleFilesTool struct {
	pathHelper *PathHelper
}

const (
	readMultipleFilesToolName = "ReadMultipleFiles"
	readMultipleFilesToolDesc = `从本地文件系统一次性读取多个文件。你可以使用此工具直接访问任何文件。
注意：通过这个工具获得的是当前最新文件内容。

用法：
- files 参数是文件读取规范对象数组，每个对象包含：
  - path（必需）：文件路径，必须是绝对路径
  - offset（可选）：起始行号，从1开始
  - limit（可选）：最大读取行数
- 默认情况下，从每个文件开头最多读取2000行
- 任何超过2000行的内容将被截断
- 结果以 cat -n 格式返回，行号从1开始
- 文件内容将按顺序合并返回
`
)

// float64Ptr 返回float64指针
func float64Ptr(f float64) *float64 {
	return &f
}

// NewReadMultipleFilesTool 创建一个新的ReadMultipleFilesTool实例
func NewReadMultipleFilesTool(projectDir string) tool.InvokableTool {
	readTool := &readMultipleFilesTool{
		pathHelper: NewPathHelper(projectDir),
	}

	// 构建 FileReadSpec schema
	fileReadSpecSchema := &openapi3.Schema{
		Type:        openapi3.TypeObject,
		Description: "文件读取规范对象",
		Properties: map[string]*openapi3.SchemaRef{
			"path": {
				Value: &openapi3.Schema{
					Type:        openapi3.TypeString,
					Description: "当前项目范围内的文件路径，必须使用绝对路径",
				},
			},
			"offset": {
				Value: &openapi3.Schema{
					Type:        openapi3.TypeInteger,
					Description: "起始读取的行号，仅当文件过大时提供该参数",
				},
			},
			"limit": {
				Value: &openapi3.Schema{
					Type:        openapi3.TypeInteger,
					Description: "读取的行数，仅当文件过大时提供该参数。",
					Min:         float64Ptr(1),
				},
			},
		},
		Required: []string{"path"},
	}

	filesSchema := &openapi3.Schema{
		Type:        openapi3.TypeArray,
		Description: "要读取的文件数组，每一项都是包含 path 及可选 offset/limit 的对象。",
		MinItems:    1,
		Items: &openapi3.SchemaRef{
			Value: fileReadSpecSchema,
		},
	}

	toolInfo := &schema.ToolInfo{
		Name: readMultipleFilesToolName,
		Desc: readMultipleFilesToolDesc,
		ParamsOneOf: schema.NewParamsOneOfByOpenAPIV3(&openapi3.Schema{
			Type: openapi3.TypeObject,
			Properties: map[string]*openapi3.SchemaRef{
				"files": {
					Value: filesSchema,
				},
			},
			Required: []string{"files"},
		}),
	}

	readInvokableTool := utils.NewTool(toolInfo, readTool.processReadMultipleFiles)
	return readInvokableTool
}

// readSingleFile 读取单个文件内容（复用Read工具的readFile方法）
func (r *readMultipleFilesTool) readSingleFile(spec *FileReadSpec) (string, error) {
	// 创建临时的Read工具实例来复用其readFile方法
	readTool := &readTool{
		pathHelper: r.pathHelper,
	}

	// 直接使用readFile方法读取内容
	content, err := readTool.readFile(spec.Path, spec.Offset, spec.Limit)
	if err != nil {
		return "", err
	}

	return content, nil
}

// processReadMultipleFiles 处理ReadMultipleFiles工具请求
func (r *readMultipleFilesTool) processReadMultipleFiles(ctx context.Context, req *ReadMultipleFilesRequest) (*ReadMultipleFilesResponse, error) {
	if len(req.Files) == 0 {
		return nil, NewToolErrorf("Files cannot be empty")
	}

	var contents []string

	for _, spec := range req.Files {
		// 获取绝对路径
		filePath := r.pathHelper.GetAbsolutePath(spec.Path)

		// 读取文件内容
		content, err := r.readSingleFile(&spec)
		if err != nil {
			var errorContent string
			if os.IsNotExist(err) {
				// 记录错误但不中断整个流程
				errorContent = fmt.Sprintf("<system-reminder>Path not found: %s</system-reminder>", spec.Path)
			} else {
				errorContent = fmt.Sprintf("<system-reminder>Read: %s failed: %s</system-reminder>", spec.Path, err.Error())
			}
			contents = append(contents, errorContent)
			continue
		}

		// 包装文件内容
		wrappedContent := fmt.Sprintf("<file path=\"%s\">\n<content>\n%s\n</content>\n</file>", filePath, content)
		contents = append(contents, wrappedContent)
	}

	// 合并所有内容
	combinedContent := strings.Join(contents, "\n")

	return &ReadMultipleFilesResponse{
		Content: combinedContent,
	}, nil
}
