package tool

import (
	"context"
	"fmt"
	"strings"

	"github.com/cloudwego/eino/components/tool"
	"github.com/cloudwego/eino/schema"
)

// ReadMultipleFilesWrapper ReadMultipleFiles 工具的包装器实现
type ReadMultipleFilesWrapper struct {
	*BaseTool
	originalTool tool.InvokableTool
}

// NewReadMultipleFilesWrapper 创建 ReadMultipleFiles 工具包装器
func NewReadMultipleFilesWrapper(projectDir string) *ReadMultipleFilesWrapper {
	// 创建原始工具实例
	originalTool := NewReadMultipleFilesTool(projectDir)

	// 创建包装器
	wrapper := &ReadMultipleFilesWrapper{
		BaseTool:     NewBaseTool("ReadMultipleFiles", "Read multiple files", nil),
		originalTool: originalTool,
	}

	return wrapper
}

// Info 返回工具信息
func (rmfw *ReadMultipleFilesWrapper) Info(ctx context.Context) (*schema.ToolInfo, error) {
	return rmfw.originalTool.Info(ctx)
}

// InvokableRun 实现 InvokableTool 接口
func (rmfw *ReadMultipleFilesWrapper) InvokableRun(ctx context.Context, argumentsInJSON string, opts ...tool.Option) (string, error) {
	return rmfw.WrapInvokableRun(ctx, argumentsInJSON, func(ctx context.Context, input string) (string, error) {
		return rmfw.originalTool.InvokableRun(ctx, input, opts...)
	})
}

// FormatEvent 格式化事件输出，显示多文件读取统计
func (rmfw *ReadMultipleFilesWrapper) FormatEvent(input, result string, err error) string {
	if err != nil {
		return fmt.Sprintf("Failed to read files: %s", err.Error())
	}

	// 统计文件数量和总行数
	fileCount := strings.Count(result, "<file path=")
	totalLines := strings.Count(result, "\n")

	return fmt.Sprintf("Read %d files, total %d lines", fileCount, totalLines)
}