package tool

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/cloudwego/eino/components/tool"
	"github.com/cloudwego/eino/components/tool/utils"
	"github.com/cloudwego/eino/schema"
	"github.com/getkin/kin-openapi/openapi3"
)

const (
	// maxResult 最大返回字符串数组长度
	maxResult = 100
)

// GrepToolRequest 定义grep工具的请求参数
type GrepToolRequest struct {
	// Pattern 要搜索的正则表达式模式
	Pattern string `json:"pattern" jsonschema:"required,description=The regular expression pattern to search for in file contents"`
	// Path 要搜索的目录路径，默认为当前工作目录
	Path string `json:"path,omitempty" jsonschema:"description=File or directory to search in. Defaults to current working directory"`
	// Glob 文件模式过滤器，例如 "*.js", "*.{ts,tsx}"
	Glob string `json:"glob,omitempty" jsonschema:"description=Glob pattern to filter files (e.g. \"*.js\", \"*.{ts,tsx}\")"`
	// Type 文件类型过滤，例如 "js", "py", "go"
	Type string `json:"type,omitempty" jsonschema:"description=File type to search (rg --type). Common types: js, py, rust, go, java, etc."`
	// OutputMode 输出模式：content、files_with_matches、count
	OutputMode string `json:"output_mode,omitempty" jsonschema:"enum=content,enum=files_with_matches,enum=count,description=Output mode: \"content\" shows matching lines, \"files_with_matches\" shows only file paths, \"count\" shows match counts. Defaults to \"files_with_matches\""`
	// B 显示匹配行前文的行数
	B int `json:"-B,omitempty" jsonschema:"description=Number of lines to show before each match. Requires output_mode: \"content\""`
	// A 显示匹配行后文的行数
	A int `json:"-A,omitempty" jsonschema:"description=Number of lines to show after each match. Requires output_mode: \"content\""`
	// C 显示匹配行前后文的行数
	C int `json:"-C,omitempty" jsonschema:"description=Number of lines to show before and after each match. Requires output_mode: \"content\""`
	// N 显示行号
	N bool `json:"-n,omitempty" jsonschema:"description=Show line numbers in output. Requires output_mode: \"content\""`
	// I 大小写不敏感搜索
	I bool `json:"-i,omitempty" jsonschema:"description=Case insensitive search"`
	// HeadLimit 限制输出结果数量
	HeadLimit int `json:"head_limit,omitempty" jsonschema:"description=Limit output to first N lines/entries"`
	// Multiline 启用多行模式匹配
	Multiline bool `json:"multiline,omitempty" jsonschema:"description=Enable multiline mode where . matches newlines and patterns can span lines"`
}

type GrepToolResponse struct {
	Result string
}

// GrepTool 基于grep命令的搜索工具
type grepTool struct {
	projectDir string
}

const (
	toolName = "RipGrep"
	toolDesc = `基于 ripgrep 构建的强大搜索工具

使用方法：

搜索任务请始终使用 Grep 工具。Grep 工具已针对正确的权限和访问进行了优化。
支持完整的正则表达式语法（例如："log.*Error"、"function\s+\w+"）
使用 glob 参数过滤文件（例如：".js"、"**/.tsx"）或使用 type 参数（例如："js"、"py"、"rust"）
输出模式："content" 显示匹配的行，"files_with_matches" 仅显示文件路径（默认），"count" 显示匹配计数
模式语法：使用 ripgrep（非 grep）- 字面大括号需要转义（使用 "interface{}" 来查找 Go 代码中的 "interface{}"）
多行匹配：默认情况下模式仅在单行内匹配。对于跨行模式如 "struct {[\s\S]*?field"，请使用 "multiline: true"`
)

// NewGrepTool 创建一个新的GrepTool实例
func NewGrepTool(projectDir string) tool.InvokableTool {
	grepTool := &grepTool{
		projectDir: projectDir,
	}

	toolInfo := &schema.ToolInfo{
		Name: toolName,
		Desc: toolDesc,
		ParamsOneOf: schema.NewParamsOneOfByOpenAPIV3(&openapi3.Schema{
			Type: openapi3.TypeObject,
			Properties: map[string]*openapi3.SchemaRef{
				"pattern": {
					Value: &openapi3.Schema{
						Type:        openapi3.TypeString,
						Description: "要在文件内容中搜索的正则表达式pattern",
					},
				},
				"path": {
					Value: &openapi3.Schema{
						Type:        openapi3.TypeString,
						Description: "要搜索的文件或目录。默认为当前工作目录。",
					},
				},
				"glob": {
					Value: &openapi3.Schema{
						Type:        openapi3.TypeString,
						Description: "用于过滤文件的 Glob 模式（例如 \"*.js\", \"*.{ts,tsx}\"） - 映射到 rg --glob",
					},
				},
				"type": {
					Value: &openapi3.Schema{
						Type:        openapi3.TypeString,
						Description: "要搜索的文件类型（rg --type）。常见类型：js, py, rust, go, java 等。对于标准文件类型，比 include 更高效。",
					},
				},
				"output_mode": {
					Value: &openapi3.Schema{
						Type:        openapi3.TypeString,
						Description: "输出模式：\"content\" 显示匹配的行（支持 -A/-B/-C 上下文，-n 行号，head_limit），\"files_with_matches\" 显示文件路径（支持 head_limit），\"count\" 显示匹配计数（支持 head_limit）。默认为 \"files_with_matches\"",
						Enum:        []interface{}{"content", "files_with_matches", "count"},
					},
				},
				"-B": {
					Value: &openapi3.Schema{
						Type:        openapi3.TypeInteger,
						Description: "在每个匹配项之前显示的行数。需要 output_mode: \"content\"，否则忽略。",
					},
				},
				"-A": {
					Value: &openapi3.Schema{
						Type:        openapi3.TypeInteger,
						Description: "在每个匹配项之后显示的行数。需要 output_mode: \"content\"，否则忽略。",
					},
				},
				"-C": {
					Value: &openapi3.Schema{
						Type:        openapi3.TypeInteger,
						Description: "在每个匹配项之前和之后显示的行数（rg -C）。需要 output_mode: \"content\"，否则忽略。",
					},
				},
				"-n": {
					Value: &openapi3.Schema{
						Type:        openapi3.TypeBoolean,
						Description: "在输出中显示行号（rg -n）。需要 output_mode: \"content\"，否则忽略。",
					},
				},
				"-i": {
					Value: &openapi3.Schema{
						Type:        openapi3.TypeBoolean,
						Description: "大小写不敏感搜索（rg -i）",
					},
				},
				"head_limit": {
					Value: &openapi3.Schema{
						Type:        openapi3.TypeInteger,
						Description: "将输出限制为前 N 行/条目，相当于 \"| head -N\"。适用于所有输出模式：content（限制输出行数），files_with_matches（限制文件路径），count（限制计数字条目）。如果未指定，则显示 ripgrep 的所有结果。",
					},
				},
				"multiline": {
					Value: &openapi3.Schema{
						Type:        openapi3.TypeBoolean,
						Description: "启用多行模式，其中 . 匹配换行符，模式可以跨行（rg -U --multiline-dotall）。默认值：false。",
					},
				},
			},
			Required: []string{"pattern"},
		}),
	}
	grepInvokableTool := utils.NewTool(toolInfo, grepTool.processGrep)
	return grepInvokableTool
}

// sortFilesByMtime 按修改时间对文件进行排序
func sortFilesByMtime(files []string, root string) []string {
	type fileStat struct {
		path string
		stat os.FileInfo
	}

	stats := make([]fileStat, 0, len(files))
	for _, f := range files {
		absPath := f
		if !filepath.IsAbs(f) {
			absPath = filepath.Join(root, f)
		}

		if stat, err := os.Stat(absPath); err == nil {
			stats = append(stats, fileStat{path: f, stat: stat})
		} else {
			// 如果无法获取文件状态，使用零时间
			stats = append(stats, fileStat{path: f, stat: nil})
		}
	}

	// 按修改时间降序排序，文件名升序排序
	sort.Slice(stats, func(i, j int) bool {
		// 修改时间降序（新文件在前）
		var timeI, timeJ time.Time
		if stats[i].stat != nil {
			timeI = stats[i].stat.ModTime()
		}
		if stats[j].stat != nil {
			timeJ = stats[j].stat.ModTime()
		}

		if !timeI.Equal(timeJ) {
			return timeI.After(timeJ) // 修改时间降序
		}

		// 修改时间相同时，按文件名升序排序
		return stats[i].path < stats[j].path
	})

	sortedFiles := make([]string, len(stats))
	for i, stat := range stats {
		sortedFiles[i] = stat.path
	}

	return sortedFiles
}

func (g *grepTool) processGrep(ctx context.Context, req *GrepToolRequest) (GrepToolResponse, error) {
	// 确定搜索路径
	searchPath := req.Path
	if searchPath == "" {
		searchPath = g.projectDir
	}

	// 转换为绝对路径
	absPath := searchPath
	if !filepath.IsAbs(searchPath) {
		absPath = filepath.Join(g.projectDir, searchPath)
	}

	// 检查路径是否存在
	if _, err := os.Stat(absPath); err != nil {
		return GrepToolResponse{}, NewToolErrorf("no such file or directory: %s", absPath)
	}

	// 设置默认输出模式
	outputMode := req.OutputMode
	if outputMode == "" {
		outputMode = "files_with_matches"
	}

	// 构建ripgrep参数
	args := make([]string, 0)

	// 根据输出模式设置参数
	switch outputMode {
	case "content":
		args = append(args, "--no-heading")
		if req.N {
			args = append(args, "-n")
		}
		if req.A > 0 {
			args = append(args, fmt.Sprintf("-A%d", req.A))
		}
		if req.B > 0 {
			args = append(args, fmt.Sprintf("-B%d", req.B))
		}
		if req.C > 0 {
			args = append(args, fmt.Sprintf("-C%d", req.C))
		}
	case "files_with_matches":
		args = append(args, "-l")
	case "count":
		args = append(args, "--count")
	}

	// 添加模式
	args = append(args, req.Pattern)

	// 添加大小写敏感选项
	if req.I {
		args = append(args, "-i")
	}

	// 添加多行模式
	if req.Multiline {
		args = append(args, "-U", "--multiline-dotall")
	}

	// 添加文件过滤
	if req.Glob != "" {
		args = append(args, fmt.Sprintf("--glob=%s", req.Glob))
	}
	if req.Type != "" {
		args = append(args, fmt.Sprintf("--type=%s", req.Type))
	}

	// 执行ripgrep搜索
	result, err := RipGrep(args, absPath)
	if err != nil {
		return GrepToolResponse{}, NewToolErrorf("failed to execute ripgrep command: %v", err)
	}

	// 限制返回结果数量
	if len(result) > maxResult {
		result = result[:maxResult]
	}

	// 记录截断信息
	truncated := 0
	if req.HeadLimit > 0 && len(result) > req.HeadLimit {
		truncated = len(result) - req.HeadLimit
		result = result[:req.HeadLimit]
	}

	// 根据输出模式处理结果
	var finalResult string

	switch outputMode {
	case "content":
		finalResult, err = g.processContentOutput(result, truncated)
	case "count":
		finalResult, err = g.processCountOutput(result, truncated)
	case "files_with_matches":
		finalResult, err = g.processFilesOutput(result, absPath, truncated)
	default:
		return GrepToolResponse{}, NewToolErrorf("unsupported output mode: %s", outputMode)
	}

	if err != nil {
		return GrepToolResponse{}, err
	}

	return GrepToolResponse{Result: finalResult}, nil
}

func (g *grepTool) processContentOutput(lines []string, truncated int) (string, error) {
	var resultLines []string
	fileSet := make(map[string]bool)

	for _, line := range lines {
		if strings.TrimSpace(line) == "" {
			continue
		}

		// 解析 ripgrep 输出格式: "file_path:line_num:content" 或 "file_path:content"
		parts := strings.SplitN(line, ":", 3)
		if len(parts) < 2 {
			continue
		}

		// 统计涉及的文件数量
		fileSet[parts[0]] = true

		// 检查文件数量是否超过限制
		if len(fileSet) > maxResult {
			return "", NewToolErrorf("<system-reminder>too many files found (%d files with matches). Please be more specific or use head_limit parameter</system-reminder>", len(fileSet))
		}

		// 直接使用原始行，保持ripgrep的格式
		resultLines = append(resultLines, line)
	}

	// 如果有截断，添加截断信息
	if truncated > 0 {
		resultLines = append(resultLines, fmt.Sprintf("<system-reminder>... [%d lines truncated] ...</system-reminder>", truncated))
	}

	return strings.Join(resultLines, "\n"), nil
}

func (g *grepTool) processCountOutput(lines []string, truncated int) (string, error) {
	var resultLines []string
	fileCount := 0

	for _, line := range lines {
		if strings.TrimSpace(line) == "" {
			continue
		}

		// 解析 ripgrep 输出格式: "file_path:count"
		parts := strings.SplitN(line, ":", 2)
		if len(parts) != 2 {
			continue
		}

		count, err := strconv.Atoi(parts[1])
		if err != nil {
			continue
		}

		// 格式化为: "file_path: N matches"
		resultLines = append(resultLines, fmt.Sprintf("%s: %d matches", parts[0], count))
		fileCount++

		// 检查文件数量是否超过限制
		if fileCount > maxResult {
			return "", NewToolErrorf("<system-reminder>too many files found (%d files with matches). Please be more specific or use head_limit parameter</system-reminder>", fileCount)
		}
	}

	// 如果有截断，添加截断信息
	if truncated > 0 {
		resultLines = append(resultLines, fmt.Sprintf("<system-reminder>... [%d files truncated] ...</system-reminder>", truncated))
	}

	return strings.Join(resultLines, "\n"), nil
}

func (g *grepTool) processFilesOutput(lines []string, root string, truncated int) (string, error) {
	// 过滤空行
	files := make([]string, 0, len(lines))
	for _, line := range lines {
		if strings.TrimSpace(line) != "" {
			files = append(files, line)
		}
	}

	// 按修改时间排序
	sortedFiles := sortFilesByMtime(files, root)

	// 限制结果数量
	if len(sortedFiles) > maxResult {
		return "", NewToolErrorf("<system-reminder>too many files found. Please be more specific or use head_limit parameter</system-reminder>")
	}

	// 如果有截断，添加截断信息
	if truncated > 0 {
		sortedFiles = append(sortedFiles, fmt.Sprintf("<system-reminder>... [%d files truncated] ...</system-reminder>", truncated))
	}

	return strings.Join(sortedFiles, "\n"), nil
}
