package tool

import (
	"github.com/cloudwego/eino/schema"
	"github.com/getkin/kin-openapi/openapi3"
)

type OpenAPIToolInfo struct {
	Name        string           `json:"name"`
	Description string           `json:"description,omitempty"`
	Parameters  *openapi3.Schema `json:"parameters"`
}

func ToolInfo2OpenAPI(info *schema.ToolInfo) *OpenAPIToolInfo {

	p, _ := info.ToOpenAPIV3()
	return &OpenAPIToolInfo{
		Name:        info.Name,
		Description: info.Desc,
		Parameters:  p,
	}
}
