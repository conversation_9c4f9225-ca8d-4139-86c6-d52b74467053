package tool

import (
	"context"
	"fmt"
	"testing"

	"github.com/pkg/errors"
	"github.com/stretchr/testify/assert"
)

func TestToolErrorHandler(t *testing.T) {
	tests := []struct {
		name           string
		err            error
		expectedResult string
		expectedError  error
	}{
		{
			name:           "handle tool error",
			err:            NewToolErrorf("test tool error"),
			expectedResult: "test tool error",
			expectedError:  nil,
		},
		{
			name:           "handle tool error with empty message",
			err:            NewToolErrorf(""),
			expectedResult: "",
			expectedError:  nil,
		},
		{
			name:           "handle tool error with formatted message",
			err:            NewToolErrorf("formatted error: %s", "test"),
			expectedResult: "formatted error: test",
			expectedError:  nil,
		},
		{
			name:           "handle wrapped tool error, should unwrap",
			err:            fmt.Errorf("wrapp error: %w", NewToolErrorf("tool error: %s", "tool message")),
			expectedResult: "tool error: tool message",
			expectedError:  nil,
		},
		{
			name:           "handle non-tool error",
			err:            errors.New("regular error"),
			expectedResult: "",
			expectedError:  errors.New("regular error"),
		},
		{
			name:           "handle nil error",
			err:            nil,
			expectedResult: "",
			expectedError:  nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			result, err := ToolErrorHandler(ctx, tt.err)

			assert.Equal(t, tt.expectedResult, result)
			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError.Error(), err.Error())
			} else {
				assert.NoError(t, err)
			}
		})
	}
}
