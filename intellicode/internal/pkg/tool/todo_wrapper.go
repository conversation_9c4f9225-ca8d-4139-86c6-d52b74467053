package tool

import (
	"context"
	"fmt"

	"github.com/cloudwego/eino/components/tool"
	"github.com/cloudwego/eino/schema"

	"intellicode/internal/pkg/todo"
)

// TodoWriteWrapper TodoWrite 工具的包装器实现
type TodoWriteWrapper struct {
	*BaseTool
	originalTool tool.InvokableTool
}

// NewTodoWriteWrapper 创建 TodoWrite 工具包装器
func NewTodoWriteWrapper(todoManager *todo.AgentTodoManager) *TodoWriteWrapper {
	// 创建原始工具实例
	originalTool := NewTodoWriteTool(todoManager)

	// 创建包装器
	wrapper := &TodoWriteWrapper{
		BaseTool:     NewBaseTool("TodoWrite", "Write todo list", nil),
		originalTool: originalTool,
	}

	// TodoWrite 显示在 UI 中
	wrapper.SetShowInUI(true)

	return wrapper
}

// Info 返回工具信息
func (tww *TodoWriteWrapper) Info(ctx context.Context) (*schema.ToolInfo, error) {
	return tww.originalTool.Info(ctx)
}

// InvokableRun 实现 InvokableTool 接口
func (tww *TodoWriteWrapper) InvokableRun(ctx context.Context, argumentsInJSON string, opts ...tool.Option) (string, error) {
	return tww.WrapInvokableRun(ctx, argumentsInJSON, func(ctx context.Context, input string) (string, error) {
		return tww.originalTool.InvokableRun(ctx, input, opts...)
	})
}

// FormatEvent 格式化事件输出，始终返回 success 状态
func (tww *TodoWriteWrapper) FormatEvent(input, result string, err error) string {
	if err != nil {
		return fmt.Sprintf("Failed to update todo list: %s", err.Error())
	}
	// 始终返回 success 状态
	return "Todo list updated"
}

// TodoReadWrapper TodoRead 工具的包装器实现
type TodoReadWrapper struct {
	*BaseTool
	originalTool tool.InvokableTool
}

// NewTodoReadWrapper 创建 TodoRead 工具包装器
func NewTodoReadWrapper(todoManager *todo.AgentTodoManager) *TodoReadWrapper {
	// 创建原始工具实例
	originalTool := NewTodoReadTool(todoManager)

	// 创建包装器
	wrapper := &TodoReadWrapper{
		BaseTool:     NewBaseTool("TodoRead", "Read todo list", nil),
		originalTool: originalTool,
	}

	// TodoRead 不显示在 UI 中
	wrapper.SetShowInUI(false)

	return wrapper
}

// Info 返回工具信息
func (trw *TodoReadWrapper) Info(ctx context.Context) (*schema.ToolInfo, error) {
	return trw.originalTool.Info(ctx)
}

// InvokableRun 实现 InvokableTool 接口
func (trw *TodoReadWrapper) InvokableRun(ctx context.Context, argumentsInJSON string, opts ...tool.Option) (string, error) {
	return trw.WrapInvokableRun(ctx, argumentsInJSON, func(ctx context.Context, input string) (string, error) {
		return trw.originalTool.InvokableRun(ctx, input, opts...)
	})
}