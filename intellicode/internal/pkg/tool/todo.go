package tool

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/cloudwego/eino/components/tool"
	"github.com/cloudwego/eino/components/tool/utils"
	"github.com/cloudwego/eino/schema"
	"github.com/getkin/kin-openapi/openapi3"
	"github.com/pkg/errors"

	"intellicode/internal/pkg/todo"
)

// TODO: 现在 Todo json 全部使用文件系统保存，运行时间长文件过多。后续需要考虑这个问题。

// Status 待办事项状态类型
type Status string

type Difficulty string

// TodoItem 定义待办事项的结构，由 LLM 生成
type TodoItem struct {
	// ID 唯一标识符
	ID string `json:"id"`
	// Content 待办事项内容
	Content string `json:"content"`
	// Status 状态：pending, in_progress, completed
	Status Status `json:"status"`
	// Difficulty 难度：easy, medium, hard
	Difficulty Difficulty `json:"difficulty"`
}

// TodoReadRequest 定义TodoRead工具的请求参数（无参数）
type TodoReadRequest struct{}

// TodoReadResponse 定义TodoRead工具的响应
type TodoReadResponse struct {
	// Todos 待办事项列表
	Todos []TodoItem `json:"todos" jsonschema:"description=The updated todo list"`
}

// TodoWriteRequest 定义TodoWrite工具的请求参数
type TodoWriteRequest struct {
	// Todos 待办事项列表
	Todos []TodoItem `json:"todos"`
}

// TodoWriteResponse 定义TodoWrite工具的响应
type TodoWriteResponse struct {
	// Message 操作结果消息
	Message string `json:"message"`
}

const (
	todoReadToolName = "TodoRead"
	todoReadToolDesc = `使用此工具读取当前的Todo List。应主动且频繁地使用此工具，以确保你随时了解当前任务列表的状态。建议在以下场景下尽可能多地使用此工具：
- 不确定下一步该做什么时
- 完成任务后，了解还有哪些剩余任务
- 在几次消息交互后，通过此工具确保任务的方向没有偏离

用法：
- 此工具不需要任何参数。请保持输入为空，不要包含任何占位对象、占位字符串或类似“input”或“empty”的键。保持为空即可。
- 此工具会返回带有status、difficulty、content的Todo List，使用这些信息跟踪进度并规划下一步
- 如果还没有Todo List，将返回一个空列表`

	todoWriteToolName = "TodoWrite"
	todoWriteToolDesc = `使用此工具创建和管理结构化任务列表。这有助于你跟踪进度、组织复杂任务，并向用户展示你的细致程度。

## 何时使用此工具
在以下场景下主动使用此工具：

1. 复杂的多步骤任务——当任务需要3个或以上不同步骤或操作时
2. 非简单且复杂的任务——需要仔细规划或多次操作的任务
3. 开始处理任务时——在开始工作前将其标记为 in_progress。理想情况下，同一时间只应有一个待办为 in_progress
4. 完成任务后——将其标记为 completed，在实现过程中发现的任何新后续任务也应添加到 Todo List 中

## 何时不应使用此工具

以下情况可跳过使用此工具：
1. 只有一个简单直接的任务
2. 任务非常简单，没有必要进行跟踪管理
3. 任务可在3个以内的简单步骤内完成

注意：如果只有一个琐碎任务，不要使用此工具，直接完成即可。`

	// 返回给模型的信息（这里将最新的 Todo List 传递给模型，减少 TodoRead 的调用）
	todoWriteSuccess = `Todo List已成功修改。请确保继续使用Todo List 来跟踪进度。
<system-reminder>
您的Todo List已更改。不要向用户明确提及此事。
以下是您Todo List的最新内容： %s
您现在不需要再次使用 TodoRead 工具，因为这是目前最新的列表。如果需要，请继续处理手头的任务。
</system-reminder>`
)

const (
	// Todo 状态常量
	StatusPending    Status = "pending"
	StatusInProgress Status = "in_progress"
	StatusCompleted  Status = "completed"

	// Todo 难度常量
	DifficultyEasy   Difficulty = "easy"
	DifficultyMedium Difficulty = "medium"
	DifficultyHard   Difficulty = "hard"
)

// validateTodoStatusOrder 验证待办事项状态顺序
// 规则：同时只能有一个item是in_progress状态
func validateTodoStatusOrder(todos []TodoItem) error {
	inProgressIndex := -1

	// 查找in_progress状态的item
	for i, todo := range todos {
		if todo.Status == StatusInProgress {
			if inProgressIndex != -1 {
				return NewToolErrorf("Multiple items with in_progress status found at index %d and %d", inProgressIndex, i)
			}
			inProgressIndex = i
		}
	}

	// 如果没有in_progress状态的item，直接返回
	if inProgressIndex == -1 {
		return nil
	}

	// NOTE: 允许模型自行选择顺序执行任务。

	// 验证in_progress前的item必须是completed
	// for i := 0; i < inProgressIndex; i++ {
	// 	if todos[i].Status != StatusCompleted {
	// 		return NewToolErrorf("Todo item at index %d must be completed before in_progress item at index %d", i, inProgressIndex)
	// 	}
	// }

	// 验证in_progress后的item必须是pending
	// for i := inProgressIndex + 1; i < len(todos); i++ {
	// 	if todos[i].Status != StatusPending {
	// 		return NewToolErrorf("Todo item at index %d must be pending after in_progress item at index %d", i, inProgressIndex)
	// 	}
	// }

	return nil
}

// NewTodoReadTool 创建一个新的TodoRead工具实例
func NewTodoReadTool(todoManager *todo.AgentTodoManager) tool.InvokableTool {
	todoReadTool := &todoReadTool{
		todoManager: todoManager,
	}

	toolInfo := &schema.ToolInfo{
		Name: todoReadToolName,
		Desc: todoReadToolDesc,
		ParamsOneOf: schema.NewParamsOneOfByOpenAPIV3(&openapi3.Schema{
			Type:       openapi3.TypeObject,
			Properties: map[string]*openapi3.SchemaRef{},
		}),
	}
	readInvokableTool := utils.NewTool(toolInfo, todoReadTool.processTodoRead)

	return readInvokableTool
}

// NewTodoWriteTool 创建一个新的TodoWrite工具实例
func NewTodoWriteTool(todoManager *todo.AgentTodoManager) tool.InvokableTool {
	todoWriteTool := &todoWriteTool{
		todoManager: todoManager,
	}

	// 手写明确定义 OpenAPIV3 Schema，避免 InferTool 的自动推断
	toolInfo := &schema.ToolInfo{
		Name: todoWriteToolName,
		Desc: todoWriteToolDesc,
		ParamsOneOf: schema.NewParamsOneOfByOpenAPIV3(&openapi3.Schema{
			Type: openapi3.TypeObject,
			Properties: map[string]*openapi3.SchemaRef{
				"todos": {
					Value: &openapi3.Schema{
						Type: openapi3.TypeArray,
						Items: &openapi3.SchemaRef{
							Value: &openapi3.Schema{
								Type: openapi3.TypeObject,
								Properties: map[string]*openapi3.SchemaRef{
									"id": {
										Value: &openapi3.Schema{
											Type:        openapi3.TypeString,
											Description: "待办事项的名字",
										},
									},
									"content": {
										Value: &openapi3.Schema{
											Type:        openapi3.TypeString,
											MinLength:   uint64(1),
											Description: "待办事项的具体内容描述，需要有明确的指向性引导任务执行",
										},
									},
									"status": {
										Value: &openapi3.Schema{
											Type: openapi3.TypeString,
											Enum: []any{string(StatusPending), string(StatusInProgress), string(StatusCompleted)},
										},
									},
									"difficulty": {
										Value: &openapi3.Schema{
											Type:        openapi3.TypeString,
											Enum:        []any{string(DifficultyEasy), string(DifficultyMedium), string(DifficultyHard)},
											Description: "待办事项任务的难度",
										},
									},
								},
								Required: []string{"id", "content", "status", "difficulty"},
							},
						},
						Description: "最新状态的 Todo List",
					},
				},
			},
			Required: []string{"todos"},
		}),
	}
	writeInvokableTool := utils.NewTool(toolInfo, todoWriteTool.processTodoWrite)
	return writeInvokableTool
}

// todoReadTool TodoRead工具实现
type todoReadTool struct {
	todoManager *todo.AgentTodoManager
}

// processTodoRead 处理TodoRead工具请求
func (t *todoReadTool) processTodoRead(ctx context.Context, req *TodoReadRequest) (*TodoReadResponse, error) {
	todosJSON, err := t.todoManager.ReadTodos()
	if err != nil {
		return nil, err
	}

	// 反序列化 JSON 字符串到 TodoItem
	var todos []TodoItem
	if err := json.Unmarshal([]byte(todosJSON), &todos); err != nil {
		// 这个错误应该不会出现，todwrite 的时候就保障了json 格式。
		return nil, errors.Wrap(err, "failed to unmarshal todos")
	}

	return &TodoReadResponse{
		Todos: todos,
	}, nil
}

// todoWriteTool TodoWrite工具实现
type todoWriteTool struct {
	todoManager *todo.AgentTodoManager
}

// processTodoWrite 处理TodoWrite工具请求
func (t *todoWriteTool) processTodoWrite(ctx context.Context, req *TodoWriteRequest) (*TodoWriteResponse, error) {
	// 验证数据合法性
	if err := validateTodos(req.Todos); err != nil {
		return nil, err
	}

	// ReadTodos 保证无数据返回"[]", 有数据肯定是json valid 数据，所以这里忽略错误。
	var currentTodos []TodoItem
	currentTodosJSON, _ := t.todoManager.ReadTodos()
	_ = json.Unmarshal([]byte(currentTodosJSON), &currentTodos)

	// 如果当前所有 todo 都已完成，需要创建新的 todo 文件
	if isAllCompleted(currentTodos) {
		if err := t.todoManager.CreateNewTodoFile(); err != nil {
			return nil, errors.Wrap(err, "failed to create new todo file")
		}
	}

	todosJSON, err := json.MarshalIndent(req.Todos, "", "  ")
	if err != nil {
		return nil, NewToolErrorf("Todo data invlid, failed to marshal todos: %s", err)
	}

	// 写入文件
	if err := t.todoManager.WriteTodos(string(todosJSON)); err != nil {
		return nil, err
	}

	return &TodoWriteResponse{
		Message: fmt.Sprintf(todoWriteSuccess, string(todosJSON)),
	}, nil
}

// validateTodos 验证 todo 数据合法性
func validateTodos(todos []TodoItem) error {
	for i, todo := range todos {
		if todo.ID == "" {
			return NewToolErrorf("Todo item at index %d is missing ID", i)
		}
		if todo.Content == "" {
			return NewToolErrorf("Todo item at index %d is missing content", i)
		}
		if todo.Status != StatusPending && todo.Status != StatusInProgress && todo.Status != StatusCompleted {
			return NewToolErrorf("Todo item at index %d has invalid status: %s", i, todo.Status)
		}
		if todo.Difficulty != DifficultyEasy && todo.Difficulty != DifficultyMedium && todo.Difficulty != DifficultyHard {
			return NewToolErrorf("Todo item at index %d has invalid difficulty: %s", i, todo.Difficulty)
		}
	}
	return validateTodoStatusOrder(todos)
}

// isAllCompleted 判断所有 todo 是否都已完成
func isAllCompleted(todos []TodoItem) bool {
	if len(todos) == 0 {
		return false
	}

	for _, todo := range todos {
		if todo.Status != StatusCompleted {
			return false
		}
	}
	return true
}
