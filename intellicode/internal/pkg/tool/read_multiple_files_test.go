package tool

import (
	"context"
	"os"
	"path/filepath"
	"strings"
	"testing"
)

func TestNewReadMultipleFilesTool(t *testing.T) {
	tempDir := t.TempDir()
	tool := NewReadMultipleFilesTool(tempDir)
	
	if tool == nil {
		t.Fatal("NewReadMultipleFilesTool should return a non-nil tool")
	}
}






func TestReadMultipleFiles_Success(t *testing.T) {
	tempDir := t.TempDir()
	tool := &readMultipleFilesTool{
		pathHelper: NewPathHelper(tempDir),
	}
	
	// 创建测试文件
	file1 := filepath.Join(tempDir, "test1.txt")
	file2 := filepath.Join(tempDir, "test2.txt")
	
	err := os.WriteFile(file1, []byte("Line 1\nLine 2\nLine 3\nLine 4\nLine 5\n"), 0644)
	if err != nil {
		t.Fatalf("Failed to create test file1: %v", err)
	}
	
	err = os.WriteFile(file2, []byte("Content 1\nContent 2\nContent 3\n"), 0644)
	if err != nil {
		t.Fatalf("Failed to create test file2: %v", err)
	}
	
	offset := 2
	limit := 3
	
	req := &ReadMultipleFilesRequest{
		Files: []FileReadSpec{
			{
				Path:   file1,
				Offset: &offset,
				Limit:  &limit,
			},
			{
				Path: file2,
			},
		},
	}
	
	ctx := context.Background()
	resp, err := tool.processReadMultipleFiles(ctx, req)
	if err != nil {
		t.Fatalf("Failed to process request: %v", err)
	}
	
	// 检查是否只读取了指定的行
	if !strings.Contains(resp.Content, "Line 2") {
		t.Error("Expected content to contain 'Line 2'")
	}
	if !strings.Contains(resp.Content, "Line 4") {
		t.Error("Expected content to contain 'Line 4'")
	}
	if strings.Contains(resp.Content, "Line 5") {
		t.Error("Content should not contain 'Line 5' due to limit")
	}
	
	// 检查第二个文件的内容
	if !strings.Contains(resp.Content, "Content 1") {
		t.Error("Expected content to contain 'Content 1'")
	}
	if !strings.Contains(resp.Content, "Content 2") {
		t.Error("Expected content to contain 'Content 2'")
	}
	if !strings.Contains(resp.Content, "Content 3") {
		t.Error("Expected content to contain 'Content 3'")
	}
}

func TestReadMultipleFiles_EmptyFiles(t *testing.T) {
	tempDir := t.TempDir()
	tool := &readMultipleFilesTool{
		pathHelper: NewPathHelper(tempDir),
	}
	
	req := &ReadMultipleFilesRequest{
		Files: []FileReadSpec{},
	}
	
	ctx := context.Background()
	_, err := tool.processReadMultipleFiles(ctx, req)
	if err == nil {
		t.Error("Expected error for empty files, got nil")
	}
}

func TestReadMultipleFiles_FileNotFound(t *testing.T) {
	tempDir := t.TempDir()
	tool := &readMultipleFilesTool{
		pathHelper: NewPathHelper(tempDir),
	}
	
	req := &ReadMultipleFilesRequest{
		Files: []FileReadSpec{{Path: "/nonexistent/file.txt"}},
	}
	
	ctx := context.Background()
	resp, err := tool.processReadMultipleFiles(ctx, req)
	if err != nil {
		t.Fatalf("Unexpected error: %v", err)
	}
	
	// 应该包含错误信息而不是失败
	if !strings.Contains(resp.Content, "File not found") {
		t.Errorf("Expected content to contain error message, got: %s", resp.Content)
	}
}

func TestReadMultipleFiles_MixedSuccessAndFailure(t *testing.T) {
	tempDir := t.TempDir()
	tool := &readMultipleFilesTool{
		pathHelper: NewPathHelper(tempDir),
	}
	
	// 创建存在的文件
	file1 := filepath.Join(tempDir, "test1.txt")
	err := os.WriteFile(file1, []byte("Valid content"), 0644)
	if err != nil {
		t.Fatalf("Failed to create test file: %v", err)
	}
	
	req := &ReadMultipleFilesRequest{
		Files: []FileReadSpec{
			{Path: file1},
			{Path: "/nonexistent/file.txt"},
			{Path: "missing.txt"},
		},
	}
	
	ctx := context.Background()
	resp, err := tool.processReadMultipleFiles(ctx, req)
	if err != nil {
		t.Fatalf("Failed to process request: %v", err)
	}
	
	// 应该包含成功文件的内容
	if !strings.Contains(resp.Content, "Valid content") {
		t.Error("Expected content to contain valid file content")
	}
	
	// 应该包含失败文件的错误信息
	if !strings.Contains(resp.Content, "File not found") {
		t.Errorf("Expected content to contain error message for missing file, got: %s", resp.Content)
	}
}


func TestReadMultipleFiles_WithOffsetOnly(t *testing.T) {
	tempDir := t.TempDir()
	tool := &readMultipleFilesTool{
		pathHelper: NewPathHelper(tempDir),
	}
	
	// 创建测试文件
	file := filepath.Join(tempDir, "test.txt")
	content := "Line 1\nLine 2\nLine 3\nLine 4\nLine 5\n"
	
	err := os.WriteFile(file, []byte(content), 0644)
	if err != nil {
		t.Fatalf("Failed to create test file: %v", err)
	}
	
	offset := 3
	
	req := &ReadMultipleFilesRequest{
		Files: []FileReadSpec{
			{
				Path:   file,
				Offset: &offset,
			},
		},
	}
	
	ctx := context.Background()
	resp, err := tool.processReadMultipleFiles(ctx, req)
	if err != nil {
		t.Fatalf("Failed to process request: %v", err)
	}
	
	// 检查是否从第3行开始读取
	if !strings.Contains(resp.Content, "Line 3") {
		t.Error("Expected content to contain 'Line 3'")
	}
	if !strings.Contains(resp.Content, "Line 4") {
		t.Error("Expected content to contain 'Line 4'")
	}
	if !strings.Contains(resp.Content, "Line 5") {
		t.Error("Expected content to contain 'Line 5'")
	}
	if strings.Contains(resp.Content, "Line 1") {
		t.Error("Content should not contain 'Line 1' due to offset")
	}
}

func TestReadMultipleFiles_WithLimitOnly(t *testing.T) {
	tempDir := t.TempDir()
	tool := &readMultipleFilesTool{
		pathHelper: NewPathHelper(tempDir),
	}
	
	// 创建测试文件
	file := filepath.Join(tempDir, "test.txt")
	content := "Line 1\nLine 2\nLine 3\nLine 4\nLine 5\n"
	
	err := os.WriteFile(file, []byte(content), 0644)
	if err != nil {
		t.Fatalf("Failed to create test file: %v", err)
	}
	
	limit := 2
	
	req := &ReadMultipleFilesRequest{
		Files: []FileReadSpec{
			{
				Path:  file,
				Limit: &limit,
			},
		},
	}
	
	ctx := context.Background()
	resp, err := tool.processReadMultipleFiles(ctx, req)
	if err != nil {
		t.Fatalf("Failed to process request: %v", err)
	}
	
	// 检查是否只读取了前2行
	if !strings.Contains(resp.Content, "Line 1") {
		t.Error("Expected content to contain 'Line 1'")
	}
	if !strings.Contains(resp.Content, "Line 2") {
		t.Error("Expected content to contain 'Line 2'")
	}
	if strings.Contains(resp.Content, "Line 3") {
		t.Error("Content should not contain 'Line 3' due to limit")
	}
	if strings.Contains(resp.Content, "Line 4") {
		t.Error("Content should not contain 'Line 4' due to limit")
	}
	if strings.Contains(resp.Content, "Line 5") {
		t.Error("Content should not contain 'Line 5' due to limit")
	}
}

func TestReadMultipleFiles_InvalidSpec(t *testing.T) {
	tempDir := t.TempDir()
	tool := &readMultipleFilesTool{
		pathHelper: NewPathHelper(tempDir),
	}
	
	req := &ReadMultipleFilesRequest{
		Files: []FileReadSpec{
			{Path: ""}, // 空路径测试
		},
	}
	
	ctx := context.Background()
	resp, err := tool.processReadMultipleFiles(ctx, req)
	if err != nil {
		t.Fatalf("Unexpected error: %v", err)
	}
	
	// 应该包含文件读取错误信息
	if !strings.Contains(resp.Content, "Read:") && !strings.Contains(resp.Content, "Path not found") {
		t.Error("Expected content to contain file reading error message")
	}
}

func TestReadMultipleFiles_EmptyPath(t *testing.T) {
	tempDir := t.TempDir()
	tool := &readMultipleFilesTool{
		pathHelper: NewPathHelper(tempDir),
	}
	
	req := &ReadMultipleFilesRequest{
		Files: []FileReadSpec{
			{Path: ""},
		},
	}
	
	ctx := context.Background()
	resp, err := tool.processReadMultipleFiles(ctx, req)
	if err != nil {
		t.Fatalf("Unexpected error: %v", err)
	}
	
	// 应该包含错误信息
	if !strings.Contains(resp.Content, "Read:") && !strings.Contains(resp.Content, "Path not found") {
		t.Error("Expected content to contain error message")
	}
}

func TestReadMultipleFiles_NullValues(t *testing.T) {
	tempDir := t.TempDir()
	tool := &readMultipleFilesTool{
		pathHelper: NewPathHelper(tempDir),
	}
	
	// 创建测试文件
	file := filepath.Join(tempDir, "test.txt")
	err := os.WriteFile(file, []byte("Content"), 0644)
	if err != nil {
		t.Fatalf("Failed to create test file: %v", err)
	}
	
	req := &ReadMultipleFilesRequest{
		Files: []FileReadSpec{
			{Path: file}, // offset 和 limit 默认为 nil
		},
	}
	
	ctx := context.Background()
	resp, err := tool.processReadMultipleFiles(ctx, req)
	if err != nil {
		t.Fatalf("Failed to process request: %v", err)
	}
	
	if !strings.Contains(resp.Content, "Content") {
		t.Error("Expected content to contain 'Content'")
	}
}

func TestReadMultipleFiles_LargeNumber(t *testing.T) {
	tempDir := t.TempDir()
	tool := &readMultipleFilesTool{
		pathHelper: NewPathHelper(tempDir),
	}
	
	// 创建测试文件
	file := filepath.Join(tempDir, "test.txt")
	content := "Line 1\nLine 2\nLine 3\n"
	
	err := os.WriteFile(file, []byte(content), 0644)
	if err != nil {
		t.Fatalf("Failed to create test file: %v", err)
	}
	
	// 测试大数字处理
	offset := 1
	limit := 100
	req := &ReadMultipleFilesRequest{
		Files: []FileReadSpec{
			{
				Path:   file,
				Offset: &offset,
				Limit:  &limit, // 超过实际行数
			},
		},
	}
	
	ctx := context.Background()
	resp, err := tool.processReadMultipleFiles(ctx, req)
	if err != nil {
		t.Fatalf("Failed to process request: %v", err)
	}
	
	// 应该能正常读取所有剩余内容
	if !strings.Contains(resp.Content, "Line 2") {
		t.Error("Expected content to contain 'Line 2'")
	}
	if !strings.Contains(resp.Content, "Line 3") {
		t.Error("Expected content to contain 'Line 3'")
	}
}