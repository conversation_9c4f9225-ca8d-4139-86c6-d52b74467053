package tool

import (
	"context"
	"fmt"

	"github.com/cloudwego/eino/components/tool"
	"github.com/cloudwego/eino/schema"
	"github.com/getkin/kin-openapi/openapi3"

	"intellicode/internal/pkg/emitter"
	pb "intellicode/pkg/proto/event"
)

// ToolWrapper 统一的工具包装器接口
type ToolWrapper interface {
	// 继承 Eino 的工具接口
	tool.InvokableTool

	// SetEventEmitter 设置事件发送器
	SetEventEmitter(emitter emitter.EventEmitter)

	// FormatEvent 格式化事件输出（用于 UI 展示）
	FormatEvent(input, result string, err error) string

	// ShouldShowInUI 是否在 UI 中显示此工具
	ShouldShowInUI() bool

	// GetName 获取工具名称
	GetName() string
}

// BaseTool 工具基础实现，提供通用功能
type BaseTool struct {
	name     string
	desc     string
	schema   *openapi3.Schema
	emitter  emitter.EventEmitter
	showInUI bool
}

// NewBaseTool 创建基础工具
func NewBaseTool(name, desc string, schema *openapi3.Schema) *BaseTool {
	return &BaseTool{
		name:     name,
		desc:     desc,
		schema:   schema,
		emitter:  emitter.NewNopEmitter(), // 默认空实现
		showInUI: true,                  // 默认显示
	}
}

// Info 实现 InvokableTool 接口
func (bt *BaseTool) Info(ctx context.Context) (*schema.ToolInfo, error) {
	return &schema.ToolInfo{
		Name:        bt.name,
		Desc:        bt.desc,
		ParamsOneOf: schema.NewParamsOneOfByOpenAPIV3(bt.schema),
	}, nil
}

// SetEventEmitter 设置事件发送器
func (bt *BaseTool) SetEventEmitter(emitter emitter.EventEmitter) {
	bt.emitter = emitter
}

// GetEventEmitter 获取事件发送器
func (bt *BaseTool) GetEventEmitter() emitter.EventEmitter {
	return bt.emitter
}

// GetName 获取工具名称
func (bt *BaseTool) GetName() string {
	return bt.name
}

// ShouldShowInUI 是否在 UI 中显示
func (bt *BaseTool) ShouldShowInUI() bool {
	return bt.showInUI
}

// SetShowInUI 设置 UI 显示策略
func (bt *BaseTool) SetShowInUI(show bool) {
	bt.showInUI = show
}

// FormatEvent 默认的事件格式化实现
func (bt *BaseTool) FormatEvent(input, result string, err error) string {
	if err != nil {
		return fmt.Sprintf("Error: %s", err.Error())
	}
	return result
}

// WrapInvokableRun 统一的工具执行包装方法
func (bt *BaseTool) WrapInvokableRun(ctx context.Context, input string, businessLogic func(context.Context, string) (string, error)) (string, error) {
	// 1. 发送 toolstart 事件
	if bt.ShouldShowInUI() {
		bt.emitter.EmitToolStart(ctx, bt.name, input)
	}

	// 2. 执行业务逻辑
	result, err := businessLogic(ctx, input)

	// 3. 发送 toolend 事件
	if bt.ShouldShowInUI() {
		status := pb.ToolStatus_TOOL_STATUS_SUCCESS
		if err != nil {
			status = pb.ToolStatus_TOOL_STATUS_ERROR
		}
		output := bt.FormatEvent(input, result, err)
		bt.emitter.EmitToolEnd(ctx, bt.name, input, output, status)
	}

	return result, err
}