package tool

import (
	"context"
	"io"
	"testing"

	"github.com/cloudwego/eino/components/tool"
	"github.com/cloudwego/eino/schema"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/assert"
)

// testErrorTool 实现了一个用于测试的工具，它的方法总是返回错误
type testErrorTool struct{}

func (t *testErrorTool) Info(ctx context.Context) (*schema.ToolInfo, error) {
	return nil, nil
}

func (t *testErrorTool) InvokableRun(ctx context.Context, argumentsInJSON string, opts ...tool.Option) (string, error) {
	return "", errors.New("test error")
}

func (t *testErrorTool) StreamableRun(ctx context.Context, argumentsInJSON string, opts ...tool.Option) (*schema.StreamReader[string], error) {
	return nil, errors.New("test stream error")
}

// TestWrapToolWithErrorHandler 测试使用错误处理器包装工具的功能
func TestWrapToolWithErrorHandler(t *testing.T) {
	ctx := context.Background()

	// 测试错误处理器不返回任何 error 的情况
	nt := WrapToolWithErrorHandler(&testErrorTool{}, func(_ context.Context, err error) (string, error) {
		return err.Error(), nil
	})

	// 测试 InvokableTool 接口
	result, err := nt.(tool.InvokableTool).InvokableRun(ctx, "")
	assert.NoError(t, err)
	assert.Equal(t, "test error", result)

	// 测试 StreamableTool 接口
	streamResult, err := nt.(tool.StreamableTool).StreamableRun(ctx, "")
	assert.NoError(t, err)
	chunk, err := streamResult.Recv()
	assert.NoError(t, err)
	assert.Equal(t, "test stream error", chunk)
	_, err = streamResult.Recv()
	assert.True(t, errors.Is(err, io.EOF))
}

// TestWrapInvokableToolWithErrorHandler 测试单独包装 InvokableTool 的功能
func TestWrapInvokableToolWithErrorHandler(t *testing.T) {
	ctx := context.Background()

	// 测试错误处理器返回错误消息和新错误的情况
	wrappedTool := WrapInvokableToolWithErrorHandler(&testErrorTool{}, func(_ context.Context, err error) (string, error) {
		return err.Error(), errors.Wrap(err, "wrapped error")
	})

	result, err := wrappedTool.InvokableRun(ctx, "")
	assert.Error(t, err)
	assert.Equal(t, "test error", result)
	assert.Contains(t, err.Error(), "wrapped error")
	assert.ErrorContains(t, err, "test error")
}

// TestWrapStreamableToolWithErrorHandler 测试单独包装 StreamableTool 的功能
func TestWrapStreamableToolWithErrorHandler(t *testing.T) {
	ctx := context.Background()

	// 测试错误处理器返回错误消息和nil错误的情况
	wrappedStreamTool := WrapStreamableToolWithErrorHandler(&testErrorTool{}, func(_ context.Context, err error) (string, error) {
		return err.Error(), nil
	})

	streamResult, err := wrappedStreamTool.StreamableRun(ctx, "")
	assert.NoError(t, err)

	chunk, err := streamResult.Recv()
	assert.NoError(t, err)
	assert.Equal(t, "test stream error", chunk)
	_, err = streamResult.Recv()
	assert.True(t, errors.Is(err, io.EOF))

	// 测试错误处理器返回错误消息和新错误的情况
	wrappedStreamTool = WrapStreamableToolWithErrorHandler(&testErrorTool{}, func(_ context.Context, err error) (string, error) {
		return err.Error(), errors.Wrap(err, "wrapped stream error")
	})

	streamResult, err = wrappedStreamTool.StreamableRun(ctx, "")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "wrapped stream error")
	// 当错误处理器返回错误时，StreamableRun 应该返回一个包含错误消息的流读取器
	assert.NotNil(t, streamResult)
	chunk, err = streamResult.Recv()
	assert.NoError(t, err)
	assert.Equal(t, "test stream error", chunk)
	_, err = streamResult.Recv()
	assert.True(t, errors.Is(err, io.EOF))
}

// TestCombinedErrorWrapper 测试同时实现了 InvokableTool 和 StreamableTool 接口的包装器
func TestCombinedErrorWrapper(t *testing.T) {
	ctx := context.Background()

	// 创建一个错误处理器，它会包装错误并返回自定义消息
	errorHandler := func(_ context.Context, err error) (string, error) {
		return "处理后的错误: " + err.Error(), errors.Wrap(err, "combined wrapper error")
	}

	// 使用 WrapToolWithErrorHandler 包装同时实现了两个接口的工具
	combinedTool := WrapToolWithErrorHandler(&testErrorTool{}, errorHandler)

	// 测试 InvokableTool 接口
	result, err := combinedTool.(tool.InvokableTool).InvokableRun(ctx, "")
	assert.Error(t, err)
	assert.Equal(t, "处理后的错误: test error", result)
	assert.Contains(t, err.Error(), "combined wrapper error")
	assert.Contains(t, err.Error(), "test error")

	// 测试 StreamableTool 接口
	streamResult, err := combinedTool.(tool.StreamableTool).StreamableRun(ctx, "")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "combined wrapper error")
	assert.Contains(t, err.Error(), "test stream error")

	// 验证流读取器包含了处理后的错误消息
	assert.NotNil(t, streamResult)
	chunk, err := streamResult.Recv()
	assert.NoError(t, err)
	assert.Equal(t, "处理后的错误: test stream error", chunk)
	_, err = streamResult.Recv()
	assert.True(t, errors.Is(err, io.EOF))
}

// 只实现 BaseTool 接口的测试工具
type baseOnlyTool struct{}

func (t *baseOnlyTool) Info(ctx context.Context) (*schema.ToolInfo, error) {
	return nil, nil
}

// TestWrapBaseOnlyTool 测试包装只实现了 BaseTool 接口的工具
func TestWrapBaseOnlyTool(t *testing.T) {
	// 创建一个错误处理器
	errorHandler := func(_ context.Context, err error) (string, error) {
		return "处理后的错误", errors.Wrap(err, "base tool error")
	}

	// 包装只实现了 BaseTool 接口的工具
	wrappedTool := WrapToolWithErrorHandler(&baseOnlyTool{}, errorHandler)

	// 验证返回的是原始工具
	_, ok := wrappedTool.(*baseOnlyTool)
	assert.True(t, ok, "当工具既不是 InvokableTool 也不是 StreamableTool 时，应该返回原始工具")
}
