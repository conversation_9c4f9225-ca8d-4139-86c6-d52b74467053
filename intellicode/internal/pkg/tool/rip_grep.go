package tool

import (
	"context"
	"intellicode/internal/pkg/log"
	"intellicode/internal/plugin/utils"
	"intellicode/third_party/ripgrep/bin"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"strings"
	"sync"
	"time"

	"github.com/pkg/errors"
)

var (
	alreadyDoneSignCheck bool
	signCheckMutex       sync.Mutex
)

func get_bin_name(dir string) string {
	if runtime.GOOS == "windows" {
		return filepath.Join(dir, "rg.exe")
	}
	return filepath.Join(dir, "rg")
}

func ensure_bin_file() (string, error) {
	dir := utils.GetRipGrepDir()
	// ensure dir exist
	err := os.MkdirAll(dir, 0755)
	if err != nil {
		return "", errors.Wrapf(err, "failed to create ripgrep directory %s", dir)
	}

	binPath := get_bin_name(dir)

	if _, err = os.Stat(binPath); err == nil {
		// bin exist
		return binPath, nil
	} else if !os.IsNotExist(err) {
		return "", errors.Wrapf(err, "failed to check ripgrep binary %s", binPath)
	}

	err = bin.CopyToBinPath(binPath)
	if err != nil {
		return "", errors.Wrapf(err, "failed to copy ripgrep binary to %s", binPath)
	}
	return binPath, nil

}

func ripgrep_path() (string, error) {
	if path, err := exec.LookPath("rg"); err == nil {
		return path, nil
	}
	return ensure_bin_file()
}

// codesignRipgrepIfNecessary 在 macOS 上检查并签名 ripgrep 二进制文件
func codesignRipgrepIfNecessary() error {
	signCheckMutex.Lock()
	defer signCheckMutex.Unlock()

	if runtime.GOOS != "darwin" || alreadyDoneSignCheck {
		return nil
	}

	alreadyDoneSignCheck = true

	ripgrepPath, err := ripgrep_path()
	if err != nil {
		return errors.Wrap(err, "failed to get ripgrep path")
	}

	// 检查 ripgrep 是否已签名
	cmd := exec.Command("codesign", "-vv", "-d", ripgrepPath)
	output, err := cmd.CombinedOutput()
	if err != nil {
		// 如果 codesign 命令失败，可能是因为文件未签名或其他原因
		// 我们继续尝试签名
		log.Debugf("codesign check for ripgrep failed: %v", err)
	}

	lines := strings.Split(string(output), "\n")
	needsSigned := false
	for _, line := range lines {
		if strings.Contains(line, "linker-signed") {
			needsSigned = true
			break
		}
	}

	if !needsSigned {
		log.Debugf("ripgrep is already signed")
		return nil
	}

	log.Debugf("signed for ripgrep")
	signCmd := exec.Command(
		"codesign",
		"--sign",
		"-",
		"--force",
		"--preserve-metadata=entitlements,requirements,flags,runtime",
		ripgrepPath,
	)
	if err := signCmd.Run(); err != nil {
		return errors.Wrapf(err, "failed to sign ripgrep at %s", ripgrepPath)
	}

	// 移除隔离属性
	quarantineCmd := exec.Command("xattr", "-d", "com.apple.quarantine", ripgrepPath)
	if err := quarantineCmd.Run(); err != nil {
		// 移除隔离属性失败不是致命错误，只记录但不返回错误
		// 因为文件可能本来就没有隔离属性
		log.Debugf("failed to remove quarantine attribute from ripgrep: %v", err)
	}

	return nil
}

// RipGrep 执行 ripgrep 命令并返回结果
// 参数:
//
//	args: ripgrep 命令行参数
//	path: 搜索目标路径
//
// 返回:
//
//	匹配的文件列表
func RipGrep(args []string, path string) ([]string, error) {
	err := codesignRipgrepIfNecessary()
	if err != nil {
		return nil, errors.Wrap(err, "failed to codesign ripgrep")
	}

	ripgrepPath, err := ripgrep_path()
	if err != nil {
		return nil, errors.Wrap(err, "failed to get ripgrep path")
	}

	// 构建完整的命令参数
	cmdArgs := append(args, path)
	log.Debugf("ripgrep called: %s %s", ripgrepPath, strings.Join(cmdArgs, " "))

	// 创建带超时的上下文
	ctx, cancel := context.WithTimeout(context.Background(), 4*time.Second)
	defer cancel()

	// 创建命令
	cmd := exec.CommandContext(ctx, ripgrepPath, cmdArgs...)

	// 执行命令并捕获输出
	output, err := cmd.CombinedOutput()
	if err != nil {
		// 检查是否是超时错误
		if ctx.Err() == context.DeadlineExceeded {
			log.Debugf("ripgrep timed out")
			return []string{}, nil
		}

		// 检查退出码
		if exitError, ok := err.(*exec.ExitError); ok {
			// 错误码 1 表示未找到匹配，这是正常的
			if exitError.ExitCode() == 1 {
				return []string{}, nil
			}
			// 其他错误码记录错误信息
			log.Errorf("ripgrep error: %s", string(output))
			return []string{}, nil
		}

		// 其他类型的错误
		return nil, NewToolErrorf("failed to execute ripgrep command: %s", err)
	}

	// 处理输出结果
	outputStr := strings.TrimSpace(string(output))

	if outputStr == "" {
		return []string{}, nil
	}

	// 按行分割并过滤空行
	lines := strings.Split(outputStr, "\n")
	result := make([]string, 0, len(lines))
	for _, line := range lines {
		if strings.TrimSpace(line) != "" {
			result = append(result, line)
		}
	}

	return result, nil
}

// ListAllContentFiles 使用 ripgrep 搜索任意字符，这会匹配所有非空文件。
// 利用这个 trick 可以得到一个项目结构，rg 会处理 ignore 文件。
func ListAllContentFiles(path string, limit *int) ([]string, error) {
	return ListAllContentFilesWithIgnore(path, limit, nil)
}

// ListAllContentFilesWithIgnore 使用 ripgrep 搜索任意字符，支持额外的忽略模式
func ListAllContentFilesWithIgnore(path string, limit *int, ignorePatterns []string) ([]string, error) {
	args := []string{"-l", "."}
	
	// 添加额外的忽略模式
	for _, pattern := range ignorePatterns {
		args = append(args, "--glob", "!"+pattern)
	}
	
	results, err := RipGrep(args, path)
	if err != nil {
		log.Errorf("failed to list content files: %v", err)
		return []string{}, nil
	}

	if limit != nil && len(results) > *limit {
		return results[:*limit], nil
	}
	return results, nil
}
