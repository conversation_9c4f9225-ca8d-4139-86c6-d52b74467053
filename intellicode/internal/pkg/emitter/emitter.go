package emitter

import (
	"context"
	
	pb "intellicode/pkg/proto/event"
)

// EventEmitter 事件发送器接口
type EventEmitter interface {
	// EmitToolStart 发送工具开始事件
	EmitToolStart(ctx context.Context, name, input string)

	// EmitToolEnd 发送工具结束事件
	EmitToolEnd(ctx context.Context, name, input, output string, status pb.ToolStatus)

	// EmitAssistant 发送助手流式响应事件
	EmitAssistant(ctx context.Context, chunk string)

	// EmitSuggestions 发送修复建议事件
	EmitSuggestions(ctx context.Context, suggestions []*pb.Suggestion)

	// EmitSessionStart 发送会话开始事件
	EmitSessionStart(ctx context.Context, sessionId string, reviewFiles []string)

	// EmitSessionEnd 发送会话结束事件
	EmitSessionEnd(ctx context.Context, sessionId string, success bool)

	// Events 获取事件通道（用于 HTTP 处理器监听）
	Events() <-chan *pb.Event

	// Close 关闭事件发送器
	Close()
}