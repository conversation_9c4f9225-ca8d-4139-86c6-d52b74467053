package emitter

import (
	pb "intellicode/pkg/proto/event"
)

// StringToToolStatus 将字符串转换为工具状态枚举
func StringToToolStatus(status string) pb.ToolStatus {
	switch status {
	case "running":
		return pb.ToolStatus_TOOL_STATUS_RUNNING
	case "success":
		return pb.ToolStatus_TOOL_STATUS_SUCCESS
	case "error":
		return pb.ToolStatus_TOOL_STATUS_ERROR
	default:
		return pb.ToolStatus_TOOL_STATUS_UNSPECIFIED
	}
}

// ToolStatusToString 将工具状态枚举转换为字符串（向后兼容）
func ToolStatusToString(status pb.ToolStatus) string {
	switch status {
	case pb.ToolStatus_TOOL_STATUS_RUNNING:
		return "running"
	case pb.ToolStatus_TOOL_STATUS_SUCCESS:
		return "success"
	case pb.ToolStatus_TOOL_STATUS_ERROR:
		return "error"
	default:
		return "unspecified"
	}
}