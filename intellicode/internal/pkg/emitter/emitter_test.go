package emitter

import (
	"context"
	"testing"
	"time"

	pb "intellicode/pkg/proto/event"
)

func TestDefaultEmitter(t *testing.T) {
	emitter := NewEmitter()
	defer emitter.Close()

	ctx := context.Background()

	// 测试工具开始事件
	go emitter.EmitToolStart(ctx, "TestTool", "test input")

	// 接收事件
	select {
	case event := <-emitter.Events():
		if event.Type != pb.EventType_EVENT_TYPE_TOOL_START {
			t.Errorf("Expected event type %s, got %s", pb.EventType_EVENT_TYPE_TOOL_START, event.Type)
		}
		
		data := event.GetToolStart()
		if data == nil {
			t.Error("Expected ToolStartData type")
		}
		
		if data.Name != "TestTool" {
			t.Errorf("Expected tool name 'TestTool', got '%s'", data.Name)
		}
		
		if data.Input != "test input" {
			t.Errorf("Expected input 'test input', got '%s'", data.Input)
		}
		
		if data.Status != pb.ToolStatus_TOOL_STATUS_RUNNING {
			t.<PERSON>rrorf("Expected status TOOL_STATUS_RUNNING, got %s", data.Status)
		}
	case <-time.After(1 * time.Second):
		t.Error("Timeout waiting for event")
	}
}

func TestDefaultEmitterToolEnd(t *testing.T) {
	emitter := NewEmitter()
	defer emitter.Close()

	ctx := context.Background()

	// 测试工具结束事件
	go emitter.EmitToolEnd(ctx, "TestTool", "test input", "test output", pb.ToolStatus_TOOL_STATUS_SUCCESS)

	// 接收事件
	select {
	case event := <-emitter.Events():
		if event.Type != pb.EventType_EVENT_TYPE_TOOL_END {
			t.Errorf("Expected event type %s, got %s", pb.EventType_EVENT_TYPE_TOOL_END, event.Type)
		}
		
		data := event.GetToolEnd()
		if data == nil {
			t.Error("Expected ToolEndData type")
		}
		
		if data.Name != "TestTool" {
			t.Errorf("Expected tool name 'TestTool', got '%s'", data.Name)
		}
		
		if data.Output != "test output" {
			t.Errorf("Expected output 'test output', got '%s'", data.Output)
		}
		
		if data.Status != pb.ToolStatus_TOOL_STATUS_SUCCESS {
			t.Errorf("Expected status TOOL_STATUS_SUCCESS, got %s", data.Status)
		}
	case <-time.After(1 * time.Second):
		t.Error("Timeout waiting for event")
	}
}

func TestDefaultEmitterAssistant(t *testing.T) {
	emitter := NewEmitter()
	defer emitter.Close()

	ctx := context.Background()

	// 测试助手事件
	go emitter.EmitAssistant(ctx, "test chunk")

	// 接收事件
	select {
	case event := <-emitter.Events():
		if event.Type != pb.EventType_EVENT_TYPE_ASSISTANT {
			t.Errorf("Expected event type %s, got %s", pb.EventType_EVENT_TYPE_ASSISTANT, event.Type)
		}
		
		data := event.GetAssistant()
		if data == nil {
			t.Error("Expected AssistantData type")
		}
		
		if data.Chunk != "test chunk" {
			t.Errorf("Expected chunk 'test chunk', got '%s'", data.Chunk)
		}
	case <-time.After(1 * time.Second):
		t.Error("Timeout waiting for event")
	}
}

func TestDefaultEmitterSuggestions(t *testing.T) {
	emitter := NewEmitter()
	defer emitter.Close()

	ctx := context.Background()
	suggestions := []*pb.Suggestion{
		{
			Id:          1,
			Category:    "test",
			Level:       "info",
			FilePath:    "test.go",
			StartLine:   1,
			EndLine:     5,
			Description: "test suggestion",
			OldString:   "old",
			NewString:   "new",
		},
	}

	// 测试建议事件
	go emitter.EmitSuggestions(ctx, suggestions)

	// 接收事件
	select {
	case event := <-emitter.Events():
		if event.Type != pb.EventType_EVENT_TYPE_SUGGESTIONS {
			t.Errorf("Expected event type %s, got %s", pb.EventType_EVENT_TYPE_SUGGESTIONS, event.Type)
		}
		
		data := event.GetSuggestions()
		if data == nil {
			t.Error("Expected SuggestionsData type")
		}
		
		if len(data.Suggestions) != 1 {
			t.Errorf("Expected 1 suggestion, got %d", len(data.Suggestions))
		}
		
		suggestion := data.Suggestions[0]
		if suggestion.Id != 1 {
			t.Errorf("Expected suggestion ID 1, got %d", suggestion.Id)
		}
	case <-time.After(1 * time.Second):
		t.Error("Timeout waiting for event")
	}
}

func TestNopEmitter(t *testing.T) {
	emitter := NewNopEmitter()
	defer emitter.Close()

	ctx := context.Background()

	// NopEmitter 应该不会发送任何事件
	emitter.EmitToolStart(ctx, "TestTool", "test input")
	emitter.EmitToolEnd(ctx, "TestTool", "test input", "test output", pb.ToolStatus_TOOL_STATUS_SUCCESS)
	emitter.EmitAssistant(ctx, "test chunk")
	emitter.EmitSuggestions(ctx, []*pb.Suggestion{})

	// Events() 应该返回 nil
	if emitter.Events() != nil {
		t.Error("NopEmitter.Events() should return nil")
	}
}

func TestDefaultEmitterClose(t *testing.T) {
	emitter := NewEmitter()
	
	ctx := context.Background()
	
	// 发送一些事件
	go func() {
		emitter.EmitToolStart(ctx, "TestTool", "test input")
		emitter.Close()
	}()

	// 应该能接收到事件，然后通道关闭
	eventCount := 0
	for event := range emitter.Events() {
		if event.Type == pb.EventType_EVENT_TYPE_TOOL_START {
			eventCount++
		}
	}
	
	if eventCount != 1 {
		t.Errorf("Expected 1 event, got %d", eventCount)
	}
}