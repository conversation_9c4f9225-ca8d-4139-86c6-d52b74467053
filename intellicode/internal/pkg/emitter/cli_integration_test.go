package emitter

import (
	"context"
	"testing"
	"time"

	pb "intellicode/pkg/proto/event"
)

// TestCLIEventIntegration 测试 CLI 事件集成功能
func TestCLIEventIntegration(t *testing.T) {
	// 创建事件发送器
	emitter := NewEmitter()
	defer emitter.Close()

	ctx := context.Background()
	received := make([]*pb.Event, 0)

	// 模拟 CLI 监听事件
	go func() {
		for event := range emitter.Events() {
			received = append(received, event)
		}
	}()

	// 模拟工具执行序列
	emitter.EmitToolStart(ctx, "TestTool", `{"test": "input"}`)
	emitter.EmitToolEnd(ctx, "TestTool", `{"test": "input"}`, "Tool executed successfully", pb.ToolStatus_TOOL_STATUS_SUCCESS)
	emitter.EmitAssistant(ctx, "🤖 Assistant: ")
	emitter.EmitAssistant(ctx, "This is a test response")
	emitter.EmitAssistant(ctx, "\n")
	
	// 发送修复建议
	suggestions := []*pb.Suggestion{
		{
			Id:          1,
			Category:    "code_defect",
			Level:       "Major",
			FilePath:    "test.go",
			StartLine:   10,
			EndLine:     15,
			Description: "Test suggestion",
			OldString:   "old code",
			NewString:   "new code",
		},
	}
	emitter.EmitSuggestions(ctx, suggestions)

	// 关闭发送器触发事件通道关闭
	emitter.Close()

	// 等待一段时间确保所有事件被处理
	time.Sleep(100 * time.Millisecond)

	// 验证接收到的事件
	expectedEventCount := 6 // toolstart, toolend, assistant*3, suggestions
	if len(received) != expectedEventCount {
		t.Errorf("Expected %d events, got %d", expectedEventCount, len(received))
	}

	// 验证事件类型顺序
	expectedTypes := []pb.EventType{
		pb.EventType_EVENT_TYPE_TOOL_START,
		pb.EventType_EVENT_TYPE_TOOL_END,
		pb.EventType_EVENT_TYPE_ASSISTANT,
		pb.EventType_EVENT_TYPE_ASSISTANT,
		pb.EventType_EVENT_TYPE_ASSISTANT,
		pb.EventType_EVENT_TYPE_SUGGESTIONS,
	}

	for i, expectedType := range expectedTypes {
		if i >= len(received) {
			t.Errorf("Missing event at index %d, expected type %s", i, expectedType)
			continue
		}
		if received[i].Type != expectedType {
			t.Errorf("Event %d: expected type %s, got %s", i, expectedType, received[i].Type)
		}
	}

	// 验证具体事件数据
	if len(received) >= 1 {
		if toolStart := received[0].GetToolStart(); toolStart != nil {
			if toolStart.Name != "TestTool" {
				t.Errorf("Expected tool name 'TestTool', got '%s'", toolStart.Name)
			}
			if toolStart.Status != pb.ToolStatus_TOOL_STATUS_RUNNING {
				t.Errorf("Expected status TOOL_STATUS_RUNNING, got %s", toolStart.Status)
			}
		} else {
			t.Error("Expected ToolStartData for first event")
		}
	}

	if len(received) >= 6 {
		if suggestionsData := received[5].GetSuggestions(); suggestionsData != nil {
			if len(suggestionsData.Suggestions) != 1 {
				t.Errorf("Expected 1 suggestion, got %d", len(suggestionsData.Suggestions))
			}
			if suggestionsData.Suggestions[0].Id != 1 {
				t.Errorf("Expected suggestion ID 1, got %d", suggestionsData.Suggestions[0].Id)
			}
		} else {
			t.Error("Expected SuggestionsData for last event")
		}
	}
}

// TestConcurrentEventHandling 测试并发事件处理
func TestConcurrentEventHandling(t *testing.T) {
	emitter := NewEmitter()
	defer emitter.Close()

	ctx := context.Background()
	eventCount := 0

	// 启动事件监听
	done := make(chan bool)
	go func() {
		for range emitter.Events() {
			eventCount++
		}
		done <- true
	}()

	// 并发发送事件
	go func() {
		for i := 0; i < 10; i++ {
			emitter.EmitToolStart(ctx, "Tool1", "input1")
		}
	}()

	go func() {
		for i := 0; i < 10; i++ {
			emitter.EmitAssistant(ctx, "chunk")
		}
	}()

	// 等待一段时间后关闭
	time.Sleep(50 * time.Millisecond)
	emitter.Close()

	// 等待处理完成
	<-done

	// 验证所有事件都被处理
	if eventCount != 20 {
		t.Errorf("Expected 20 events, got %d", eventCount)
	}
}