package emitter

import (
	"context"
	"sync"
	
	pb "intellicode/pkg/proto/event"
)

// DefaultEmitter EventEmitter 的默认实现
type DefaultEmitter struct {
	eventChan chan *pb.Event
	closed    bool
	mu        sync.RWMutex
}

// NewEmitter 创建新的事件发送器
func NewEmitter() *DefaultEmitter {
	return &DefaultEmitter{
		eventChan: make(chan *pb.Event, 100), // 缓冲通道防止阻塞
	}
}

// EmitToolStart 发送工具开始事件
func (e *DefaultEmitter) EmitToolStart(ctx context.Context, name, input string) {
	e.emit(&pb.Event{
		Type: pb.EventType_EVENT_TYPE_TOOL_START,
		Data: &pb.Event_ToolStart{
			ToolStart: &pb.ToolStartData{
				Name:   name,
				Input:  input,
				Status: pb.ToolStatus_TOOL_STATUS_RUNNING,
			},
		},
	})
}

// EmitToolEnd 发送工具结束事件
func (e *DefaultEmitter) EmitToolEnd(ctx context.Context, name, input, output string, status pb.ToolStatus) {
	e.emit(&pb.Event{
		Type: pb.EventType_EVENT_TYPE_TOOL_END,
		Data: &pb.Event_ToolEnd{
			ToolEnd: &pb.ToolEndData{
				Name:   name,
				Input:  input,
				Output: output,
				Status: status,
			},
		},
	})
}

// EmitAssistant 发送助手流式响应事件
func (e *DefaultEmitter) EmitAssistant(ctx context.Context, chunk string) {
	e.emit(&pb.Event{
		Type: pb.EventType_EVENT_TYPE_ASSISTANT,
		Data: &pb.Event_Assistant{
			Assistant: &pb.AssistantData{
				Chunk: chunk,
			},
		},
	})
}

// EmitSuggestions 发送修复建议事件
func (e *DefaultEmitter) EmitSuggestions(ctx context.Context, suggestions []*pb.Suggestion) {
	e.emit(&pb.Event{
		Type: pb.EventType_EVENT_TYPE_SUGGESTIONS,
		Data: &pb.Event_Suggestions{
			Suggestions: &pb.SuggestionsData{
				Suggestions: suggestions,
			},
		},
	})
}

// EmitSessionStart 发送会话开始事件
func (e *DefaultEmitter) EmitSessionStart(ctx context.Context, sessionId string, reviewFiles []string) {
	e.emit(&pb.Event{
		Type: pb.EventType_EVENT_TYPE_SESSION_START,
		Data: &pb.Event_SessionStart{
			SessionStart: &pb.SessionStartData{
				SessionId:   sessionId,
				ReviewFiles: reviewFiles,
			},
		},
	})
}

// EmitSessionEnd 发送会话结束事件
func (e *DefaultEmitter) EmitSessionEnd(ctx context.Context, sessionId string, success bool) {
	e.emit(&pb.Event{
		Type: pb.EventType_EVENT_TYPE_SESSION_END,
		Data: &pb.Event_SessionEnd{
			SessionEnd: &pb.SessionEndData{
				SessionId: sessionId,
				Success:   success,
			},
		},
	})
}

// Events 获取事件通道
func (e *DefaultEmitter) Events() <-chan *pb.Event {
	return e.eventChan
}

// Close 关闭事件发送器
func (e *DefaultEmitter) Close() {
	e.mu.Lock()
	defer e.mu.Unlock()

	if !e.closed {
		close(e.eventChan)
		e.closed = true
	}
}

// emit 内部事件发送方法
func (e *DefaultEmitter) emit(event *pb.Event) {
	e.mu.RLock()
	defer e.mu.RUnlock()

	if !e.closed {
		select {
		case e.eventChan <- event:
		default:
			// 非阻塞发送，如果通道满了就丢弃
		}
	}
}