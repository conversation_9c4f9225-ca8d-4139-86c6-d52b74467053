package emitter

import (
	"context"
	
	pb "intellicode/pkg/proto/event"
)

// NopEmitter 空的事件发送器实现，用于 CLI 兼容
type NopEmitter struct{}

// 确保 NopEmitter 实现了 EventEmitter 接口
var _ EventEmitter = (*NopEmitter)(nil)

// NewNopEmitter 创建空的事件发送器
func NewNopEmitter() *NopEmitter {
	return &NopEmitter{}
}

// EmitToolStart 空实现
func (e *NopEmitter) EmitToolStart(ctx context.Context, name, input string) {}

// EmitToolEnd 空实现
func (e *NopEmitter) EmitToolEnd(ctx context.Context, name, input, output string, status pb.ToolStatus) {}

// EmitAssistant 空实现
func (e *NopEmitter) EmitAssistant(ctx context.Context, chunk string) {}

// EmitSuggestions 空实现
func (e *NopEmitter) EmitSuggestions(ctx context.Context, suggestions []*pb.Suggestion) {}

// EmitSessionStart 空实现
func (e *NopEmitter) EmitSessionStart(ctx context.Context, sessionId string, reviewFiles []string) {}

// EmitSessionEnd 空实现
func (e *NopEmitter) EmitSessionEnd(ctx context.Context, sessionId string, success bool) {}

// Events 返回 nil 通道
func (e *NopEmitter) Events() <-chan *pb.Event {
	return nil
}

// Close 空实现
func (e *NopEmitter) Close() {}