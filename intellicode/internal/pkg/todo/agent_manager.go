package todo

import (
	"fmt"
	"os"
	"path/filepath"
	"time"

	"github.com/pkg/errors"
)

// AgentTodoManager 为每个 Agent 实例提供独立的 todo 文件管理功能
// 只负责文件的读写操作，不涉及业务逻辑和结构体定义
type AgentTodoManager struct {
	agentUUID string // Agent 的唯一标识符
	fileDir   string // JSON 文件的基础目录
	filePath  string // 当前 todo 文件的完整路径
}

// NewAgentTodoManager 创建一个新的 AgentTodoManager 实例
func NewAgentTodoManager(agentUUID string, fileDir string) *AgentTodoManager {
	return &AgentTodoManager{
		agentUUID: agentUUID,
		fileDir:   fileDir,
	}
}

// GetFileDir 返回文件基础目录
func (atm *AgentTodoManager) GetFileDir() string {
	return atm.fileDir
}

// GetAgentUUID 返回 Agent 的 UUID
func (atm *AgentTodoManager) GetAgentUUID() string {
	return atm.agentUUID
}

// GetFilePath 返回当前 todo 文件的路径
func (atm *AgentTodoManager) GetFilePath() string {
	return atm.filePath
}

func (atm *AgentTodoManager) CreateNewTodoFile() error {
	return atm.createNewTodoFile()
}

// WriteTodos 将 JSON 字符串写入 todo 文件
func (atm *AgentTodoManager) WriteTodos(todosJSON string) error {
	// 如果文件路径未设置，创建新的文件路径
	if atm.filePath == "" {
		if err := atm.createNewTodoFile(); err != nil {
			return err
		}
	}

	// 确保目录存在
	dir := filepath.Dir(atm.filePath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return errors.Wrap(err, "failed to create todo directory")
	}

	// 写入文件
	if err := os.WriteFile(atm.filePath, []byte(todosJSON), 0644); err != nil {
		return errors.Wrap(err, "failed to write todo file")
	}

	return nil
}

// ReadTodos 从文件读取 JSON 字符串
func (atm *AgentTodoManager) ReadTodos() (string, error) {
	// 如果文件路径未设置，直接返回空 JSON 数组
	if atm.filePath == "" {
		return "[]", nil
	}

	// 如果文件不存在，返回空 JSON 数组
	if _, err := os.Stat(atm.filePath); os.IsNotExist(err) {
		return "[]", nil
	}

	data, err := os.ReadFile(atm.filePath)
	if err != nil {
		return "[]", errors.Wrap(err, "failed to read todo file")
	}

	return string(data), nil
}

// createNewTodoFile 内部实现创建新的 todo 文件
func (atm *AgentTodoManager) createNewTodoFile() error {
	// 生成新的时间戳，格式：YYYYMMDD-HHMMSS
	timestamp := time.Now().Format("20060102-150405")

	// 构建文件路径：{agent-uuid}-agent-{timestamp}.json
	filename := fmt.Sprintf("%s-agent-%s.json", atm.agentUUID, timestamp)
	atm.filePath = filepath.Join(atm.fileDir, filename)

	return nil
}
