package agent

import (
	"intellicode/internal/pkg/llm"
	"intellicode/internal/pkg/prompt"
	"intellicode/internal/pkg/todo"
	
	"github.com/cloudwego/eino/components/model"
)

type BaseAgent struct {
	LlmManager  *llm.Manager
	Template    prompt.TemplateManager
	MaxStep     int // 最大步骤数
	UUID        string
	AgentModel  model.ToolCallingChatModel // 用于执行 Agent 任务的模型
	FastModel   model.BaseChatModel        // 用于快速生成的模型
	todoManager *todo.AgentTodoManager     // Agent 级别的 todo 管理器
}

// GetTodoManager 返回 Agent 的 todo 管理器
func (ba *BaseAgent) GetTodoManager() *todo.AgentTodoManager {
	return ba.todoManager
}

// InitializeTodoManager 初始化 Agent 的 todo 管理器
func (ba *BaseAgent) InitializeTodoManager(fileDir string) error {
	ba.todoManager = todo.NewAgentTodoManager(ba.UUID, fileDir)
	return nil
}
