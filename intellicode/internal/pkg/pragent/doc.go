// Package pragent 提供远程存储服务，为 Agent 系统提供代码审查数据存储能力
//
// 该包是 Python 版本 pkg/storage/pr_agent.py 和 pkg/storage/schemas.py 的 Go 实现
// 提供与远程 PR Agent 服务的完整交互能力。
//
// 主要功能：
// - 代码审查记录的 CRUD 操作
// - 审查问题的管理
// - 用户反馈处理
// - 审查状态跟踪
//
// 使用示例：
//
//	config := pragent.LoadConfigFromEnv()
//	service, err := pragent.NewRemoteService(config)
//	if err != nil {
//		log.Fatal(err)
//	}
//
//	// 创建审查记录
//	review := &pragent.PrAgentReview{
//		ReviewMD5: "abc123",
//		RepoURL:   "https://github.com/user/repo",
//		Files:     []string{"main.go", "utils.go"},
//		IDE:       "JetBrains",
//		LLMModel:  "claude-3-5-sonnet",
//	}
//
//	resp, err := service.PostReview(context.Background(), review)
//	if err != nil {
//		log.Fatal(err)
//	}
//
//	// 创建审查问题
//	issues := &pragent.PrAgentCreateIssuesRequest{
//		ReviewID: resp.Data.ReviewID,
//		Issues: []pragent.Suggestion{
//			{
//				FilePath:              "main.go",
//				StartLineNumber:       10,
//				EndLineNumber:         15,
//				Level:                 "INFO",
//				OriginalCodeSnippet:   "old code",
//				SuggestedCodeSnippet:  "new code",
//				SuggestionCategory:    "refactor",
//				SuggestionDescription: "建议重构这段代码",
//			},
//		},
//	}
//
//	_, err = service.CreateIssues(context.Background(), issues)
package pragent