package pragent

import (
	"os"
	"strconv"
	"time"
)

// LoadConfigFromEnv 从环境变量加载配置
func LoadConfigFromEnv() *Config {
	return &Config{
		BaseURL:    getEnv("PR_AGENT_URL", ""),
		Token:      getEnv("PR_AGENT_TOKEN", ""),
		CICDToken:  getEnv("CICD_TOKEN", ""),
		Timeout:    getDurationEnv("PR_AGENT_TIMEOUT", 20*time.Second),
		RetryCount: getIntEnv("PR_AGENT_RETRY_COUNT", 3),
	}
}

// LoadConfig 从参数加载配置
func LoadConfig(baseURL, token, cicdToken string, timeout time.Duration, retryCount int) *Config {
	if timeout == 0 {
		timeout = 20 * time.Second
	}
	if retryCount == 0 {
		retryCount = 3
	}
	return &Config{
		BaseURL:    baseURL,
		Token:      token,
		CICDToken:  cicdToken,
		Timeout:    timeout,
		RetryCount: retryCount,
	}
}

// Validate 验证配置
func (c *Config) Validate() error {
	if c.BaseURL == "" {
		return NewStorageError(ErrCodeConfig, "PR_AGENT_URL is required", nil)
	}
	if c.Token == "" {
		return NewStorageError(ErrCodeConfig, "PR_AGENT_TOKEN is required", nil)
	}
	return nil
}

// Helper functions
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getIntEnv(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

func getDurationEnv(key string, defaultValue time.Duration) time.Duration {
	if value := os.Getenv(key); value != "" {
		if duration, err := time.ParseDuration(value); err == nil {
			return duration
		}
	}
	return defaultValue
}