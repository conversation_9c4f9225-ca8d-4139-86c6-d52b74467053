package pragent

import (
	"time"
)

// BaseResponse 基础响应结构
type BaseResponse struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data any    `json:"data"`
}

// Suggestion 建议结构
type Suggestion struct {
	FilePath              string `json:"file_path"`
	StartLineNumber       int    `json:"start_line_number"`
	EndLineNumber         int    `json:"end_line_number"`
	Level                 string `json:"level"`
	OriginalCodeSnippet   string `json:"original_code_snippet"`
	SuggestedCodeSnippet  string `json:"suggested_code_snippet"`
	SuggestionCategory    string `json:"suggestion_category"`
	SuggestionDescription string `json:"suggestion_description"`
}

// ReviewAndIssuesReq 提交审查和问题的请求
type ReviewAndIssuesReq struct {
	RepoURL string       `json:"repo_url"`
	ReviewMD5 string     `json:"review_md5"`
	Issues  []Suggestion `json:"issues"`
}

// PrAgentFeedbackReasons 反馈原因响应
type PrAgentFeedbackReasons struct {
	BaseResponse
	Data map[string]any `json:"data"`
}

// Review 审查记录
type Review struct {
	ID        int       `json:"id"`
	RepoURL   string    `json:"repo_url"`
	ReviewMD5 string    `json:"review_md5"`
	Status    string    `json:"status"`
	TriggerID int       `json:"trigger_id"`
	TriggerBy string    `json:"trigger_by"`
	Email     string    `json:"email"`
	CreatedAt time.Time `json:"created_at"`
}

// PrAgentReviews 审查列表响应
type PrAgentReviews struct {
	BaseResponse
	Data []Review `json:"data"`
}

// PrAgentReviewDetail 审查详情响应
type PrAgentReviewDetail struct {
	BaseResponse
	Data Review `json:"data"`
}

// Issue 问题记录
type Issue struct {
	ID                   int       `json:"id"`
	IntellicodeReviewID  int       `json:"intellicode_review_id"`
	Status               string    `json:"status"`
	FilePath             string    `json:"file_path"`
	StartLineNumber      int       `json:"start_line_number"`
	EndLineNumber        int       `json:"end_line_number"`
	Level                string    `json:"level"`
	OriginalCodeSnippet  string    `json:"original_code_snippet"`
	SuggestedCodeSnippet string    `json:"suggested_code_snippet"`
	SuggestionCategory   string    `json:"suggestion_category"`
	SuggestionDescription string   `json:"suggestion_description"`
	Result               string    `json:"result"`
	Reason               string    `json:"reason"`
	OtherReason          string    `json:"other_reason"`
	CreatedAt            time.Time `json:"created_at"`
}

// PrAgentReviewIssues 问题列表响应
type PrAgentReviewIssues struct {
	BaseResponse
	Data []Issue `json:"data"`
}

// FeedbackReq 反馈请求
type FeedbackReq struct {
	ReviewID int    `json:"review_id"`
	Rating   int    `json:"rating"`
	Comment  string `json:"comment"`
}

// FeedbackResp 反馈响应
type FeedbackResp struct {
	BaseResponse
}

// PrAgentReview 创建审查请求
type PrAgentReview struct {
	ReviewMD5 string   `json:"review_md5"`
	RepoURL   string   `json:"repo_url"`
	Files     []string `json:"files"`
	IDE       string   `json:"ide"`
	LLMModel  string   `json:"llm_model"`
}

// PrAgentReviewObject 审查对象
type PrAgentReviewObject struct {
	ReviewID int `json:"review_id"`
}

// PrAgentReviewResponse 创建审查响应
type PrAgentReviewResponse struct {
	BaseResponse
	Data PrAgentReviewObject `json:"data"`
}

// PrAgentPutReviewRequest 更新审查请求
type PrAgentPutReviewRequest struct {
	Status       string `json:"status"`
	FailedReason string `json:"failed_reason"`
}

// PrAgentPutReviewResponse 更新审查响应
type PrAgentPutReviewResponse struct {
	BaseResponse
}

// PrAgentCreateIssuesRequest 创建问题请求
type PrAgentCreateIssuesRequest struct {
	ReviewID int        `json:"review_id"`
	Issues   []Suggestion `json:"issues"`
}

// PrAgentCreateIssuesResponse 创建问题响应
type PrAgentCreateIssuesResponse struct {
	BaseResponse
}