package pragent

import (
	"fmt"
)

// StorageError 存储服务错误
type StorageError struct {
	Code    int
	Message string
	Err     error
}

func (e *StorageError) Error() string {
	if e.Err != nil {
		return fmt.Sprintf("storage error [code=%d]: %s: %v", e.Code, e.Message, e.Err)
	}
	return fmt.Sprintf("storage error [code=%d]: %s", e.Code, e.Message)
}

func (e *StorageError) Unwrap() error {
	return e.Err
}

// NewStorageError 创建新的存储错误
func NewStorageError(code int, message string, err error) *StorageError {
	return &StorageError{
		Code:    code,
		Message: message,
		Err:     err,
	}
}

// Error codes
const (
	ErrCodeConfig     = 1001
	ErrCodeRequest    = 1002
	ErrCodeResponse   = 1003
	ErrCodeAuth       = 1004
	ErrCodeNotFound   = 1005
	ErrCodeValidation = 1006
)