package pragent

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

// Config 远程服务配置
type Config struct {
	BaseURL    string
	Token      string
	CICDToken  string
	Timeout    time.Duration
	RetryCount int
}

// RemoteService 远程存储服务
type RemoteService struct {
	config  *Config
	client  *http.Client
	headers map[string]string
}

// NewRemoteService 创建新的远程存储服务
func NewRemoteService(config *Config) (*RemoteService, error) {
	if config.BaseURL == "" {
		return nil, NewStorageError(ErrCodeConfig, "base URL is empty", nil)
	}
	if config.Token == "" {
		return nil, NewStorageError(ErrCodeConfig, "token is empty", nil)
	}
	if config.Timeout == 0 {
		config.Timeout = 20 * time.Second
	}
	if config.RetryCount == 0 {
		config.RetryCount = 3
	}

	httpClient := &http.Client{
		Timeout: config.Timeout,
		Transport: &http.Transport{
			MaxIdleConns:       10,
			IdleConnTimeout:    30 * time.Second,
			DisableCompression: false,
		},
	}

	headers := map[string]string{
		"X-TOKEN":            config.Token,
		"Content-Type":       "application/json",
		"Cicd-Authorization": config.CICDToken,
	}

	return &RemoteService{
		config:  config,
		client:  httpClient,
		headers: headers,
	}, nil
}

// doRequest 执行 HTTP 请求
func (s *RemoteService) doRequest(ctx context.Context, method, endpoint string, body any) (*http.Response, error) {
	url := fmt.Sprintf("%s/%s", s.config.BaseURL, endpoint)

	var bodyReader io.Reader
	if body != nil {
		jsonBody, err := json.Marshal(body)
		if err != nil {
			return nil, NewStorageError(ErrCodeValidation, "failed to marshal request body", err)
		}
		bodyReader = bytes.NewReader(jsonBody)
	}

	req, err := http.NewRequestWithContext(ctx, method, url, bodyReader)
	if err != nil {
		return nil, NewStorageError(ErrCodeRequest, "failed to create request", err)
	}

	// 设置请求头
	for key, value := range s.headers {
		req.Header.Set(key, value)
	}

	// 重试逻辑
	var lastErr error
	for i := 0; i <= s.config.RetryCount; i++ {
		resp, err := s.client.Do(req)
		if err == nil {
			return resp, nil
		}
		lastErr = err

		if i < s.config.RetryCount {
			backoff := time.Duration(i+1) * time.Second
			select {
			case <-time.After(backoff):
			case <-ctx.Done():
				return nil, ctx.Err()
			}
		}
	}

	return nil, NewStorageError(ErrCodeRequest, "request failed after retries", lastErr)
}

// decodeResponse 解码响应
func (s *RemoteService) decodeResponse(resp *http.Response, result any) error {
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return NewStorageError(ErrCodeResponse, "failed to read response body", err)
	}

	if resp.StatusCode >= 400 {
		return NewStorageError(resp.StatusCode, fmt.Sprintf("HTTP %d: %s", resp.StatusCode, string(body)), nil)
	}

	if err := json.Unmarshal(body, result); err != nil {
		return NewStorageError(ErrCodeResponse, "failed to decode response", err)
	}

	return nil
}

// PostReviewAndIssues 提交审查和问题
func (s *RemoteService) PostReviewAndIssues(ctx context.Context, req *ReviewAndIssuesReq) error {
	resp, err := s.doRequest(ctx, "POST", "reviews/issues", req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return NewStorageError(resp.StatusCode, "failed to post review and issues", nil)
	}

	return nil
}

// GetReasons 获取所有反馈理由
func (s *RemoteService) GetReasons(ctx context.Context) (*PrAgentFeedbackReasons, error) {
	resp, err := s.doRequest(ctx, "GET", "reasons", nil)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	var result PrAgentFeedbackReasons
	if err := s.decodeResponse(resp, &result); err != nil {
		return nil, err
	}

	return &result, nil
}

// GetReviews 获取代码审查记录列表
func (s *RemoteService) GetReviews(ctx context.Context, repoURL string, limit int) (*PrAgentReviews, error) {
	endpoint := fmt.Sprintf("reviews?repo_url=%s&limit=%d", repoURL, limit)
	resp, err := s.doRequest(ctx, "GET", endpoint, nil)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	var result PrAgentReviews
	if err := s.decodeResponse(resp, &result); err != nil {
		return nil, err
	}

	return &result, nil
}

// GetReviewDetail 通过 review_md5 获取审查详情
func (s *RemoteService) GetReviewDetail(ctx context.Context, reviewMD5 string) (*PrAgentReviewDetail, error) {
	endpoint := fmt.Sprintf("review?review_md5=%s", reviewMD5)
	resp, err := s.doRequest(ctx, "GET", endpoint, nil)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	var result PrAgentReviewDetail
	if err := s.decodeResponse(resp, &result); err != nil {
		return nil, err
	}

	return &result, nil
}

// GetReview 通过 review_id 获取审查详情
func (s *RemoteService) GetReview(ctx context.Context, reviewID int) (*PrAgentReviewDetail, error) {
	endpoint := fmt.Sprintf("review?review_id=%d", reviewID)
	resp, err := s.doRequest(ctx, "GET", endpoint, nil)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	var result PrAgentReviewDetail
	if err := s.decodeResponse(resp, &result); err != nil {
		return nil, err
	}

	return &result, nil
}

// GetReviewIssues 获取审查对应的问题列表
func (s *RemoteService) GetReviewIssues(ctx context.Context, reviewID int) (*PrAgentReviewIssues, error) {
	endpoint := fmt.Sprintf("reviews/%d/issues", reviewID)
	resp, err := s.doRequest(ctx, "GET", endpoint, nil)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	var result PrAgentReviewIssues
	if err := s.decodeResponse(resp, &result); err != nil {
		return nil, err
	}

	return &result, nil
}

// PostFeedback 提交反馈
func (s *RemoteService) PostFeedback(ctx context.Context, req *FeedbackReq) (*FeedbackResp, error) {
	resp, err := s.doRequest(ctx, "POST", "feedback", req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	var result FeedbackResp
	if err := s.decodeResponse(resp, &result); err != nil {
		return nil, err
	}

	return &result, nil
}

// PostReview 提交审查
func (s *RemoteService) PostReview(ctx context.Context, req *PrAgentReview) (*PrAgentReviewResponse, error) {
	resp, err := s.doRequest(ctx, "POST", "reviews", req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	var result PrAgentReviewResponse
	if err := s.decodeResponse(resp, &result); err != nil {
		return nil, err
	}

	return &result, nil
}

// PutReview 更新审查
func (s *RemoteService) PutReview(ctx context.Context, reviewID int, req *PrAgentPutReviewRequest) (*PrAgentPutReviewResponse, error) {
	endpoint := fmt.Sprintf("reviews/%d", reviewID)
	resp, err := s.doRequest(ctx, "PUT", endpoint, req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	var result PrAgentPutReviewResponse
	if err := s.decodeResponse(resp, &result); err != nil {
		return nil, err
	}

	return &result, nil
}

// CreateIssues 创建问题
func (s *RemoteService) CreateIssues(ctx context.Context, req *PrAgentCreateIssuesRequest) (*PrAgentCreateIssuesResponse, error) {
	endpoint := fmt.Sprintf("reviews/%d/issues", req.ReviewID)
	resp, err := s.doRequest(ctx, "POST", endpoint, req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	var result PrAgentCreateIssuesResponse
	if err := s.decodeResponse(resp, &result); err != nil {
		return nil, err
	}

	return &result, nil
}

