package config

// Environment defines the application environment type.
type Environment string

const (
	EnvDevelopment Environment = "development"
	EnvProduction  Environment = "production"
)

type ModelID string

const (
	Claude35Sonnet ModelID = "claude-3.5-sonnet"
	Claude37Sonnet ModelID = "claude-3.7-sonnet"
	Claude4Sonnet  ModelID = "claude-4-sonnet"
	DeepSeekR1     ModelID = "deepseek-r1"
	Qwen3          ModelID = "qwen-3"
)
