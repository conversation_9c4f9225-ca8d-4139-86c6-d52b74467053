package config

import (
	"bytes"

	"intellicode/internal/pkg/log"

	"github.com/pkg/errors"
	"github.com/spf13/viper"
)

// LoadConfigFromEmbed loads configuration from the embedded file system.
// env: 当前运行环境，用于选择对应的嵌入配置并设置到 Config 结构体中。
func LoadConfigFromEmbed(env Environment) (*Config, error) {
	log.Infof("Attempting to load configuration from embed FS. Environment: %s", env)

	appViper := viper.New()
	appViper.SetConfigType("toml") // Explicitly set config type for embed.FS

	// Get the config file content directly
	configFileBytes, err := getConfigContent(env)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to read config file for environment '%s' from embed FS", env)
	}

	// Read config from byte slice
	if err := appViper.ReadConfig(bytes.NewReader(configFileBytes)); err != nil {
		return nil, errors.Wrapf(err, "failed to parse config file for environment '%s' from embed FS", env)
	}

	var cfg Config // 使用 cfg 避免与包名 config冲突
	if errUnmarshal := appViper.Unmarshal(&cfg); errUnmarshal != nil {
		return nil, errors.Wrap(errUnmarshal, "failed to unmarshal config")
	}

	cfg.Environment = env // 设置最终的环境值

	log.Infof("Successfully loaded configuration from embed.FS for environment: %s", env)
	return &cfg, nil
}



// LoadConfigFromFileSystem loads configuration from the file system.
func LoadConfigFromFileSystem(configPath string, env Environment) (*Config, error) {
	appViper := viper.New()

	appViper.SetConfigFile(configPath)
	if errRead := appViper.ReadInConfig(); errRead != nil {
		return nil, errors.Wrapf(errRead, "failed to read config file '%s' from file system", configPath)
	}

	var cfg Config
	if errUnmarshal := appViper.Unmarshal(&cfg); errUnmarshal != nil {
		return nil, errors.Wrap(errUnmarshal, "failed to unmarshal config from file system")
	}

	cfg.Environment = env

	log.Infof("Successfully loaded configuration from file system for environment: %s", env)
	return &cfg, nil
}
