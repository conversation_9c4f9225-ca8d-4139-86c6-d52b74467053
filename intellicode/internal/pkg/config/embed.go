package config

import (
	"embed"
)

//go:embed configs/config.development.toml
var developmentConfig embed.FS
var devFile = "configs/config.development.toml"

//go:embed configs/config.production.toml
var productionConfig embed.FS
var proFile = "configs/config.production.toml"

// getConfigContent returns the appropriate config file content as bytes based on environment
func getConfigContent(env Environment) ([]byte, error) {
	switch env {
	case EnvDevelopment:
		return developmentConfig.ReadFile(devFile)
	case EnvProduction:
		return productionConfig.ReadFile(proFile)
	default:
		return developmentConfig.ReadFile(devFile)
	}
}
