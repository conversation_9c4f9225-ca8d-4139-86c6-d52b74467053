package config

// Config holds all configuration for the application.
// The values are read by viper from a config file or environment variables.
type Config struct {
	Environment    Environment    `mapstructure:"environment"`
	ModelConfigs   []ModelConfig  `mapstructure:"model_configs"`
	LangfuseConfig LangfuseConfig `mapstructure:"langfuse"`
}

type ModelConfig struct {
	ModelID   string `mapstructure:"model_id"` // 内部模型 ID 对应 ModelID
	APIKey    string `mapstructure:"api_key"`
	BaseURL   string `mapstructure:"base_url"`
	Model     string `mapstructure:"model"` // Provider 侧的模型名称
	MaxTokens int    `mapstructure:"max_tokens"`
}

// LangfuseConfig holds Langfuse configuration
type LangfuseConfig struct {
	Host      string `mapstructure:"host"`
	PublicKey string `mapstructure:"public_key"`
	SecretKey string `mapstructure:"secret_key"`
}
