package llm

import (
	"context"
	"errors"
	"fmt"
	"intellicode/internal/pkg/config"

	"github.com/cloudwego/eino-ext/components/model/openai"
	"github.com/cloudwego/eino/components/model"
)

type Manager struct {
	models map[config.ModelID]model.ToolCallingChatModel
}

func NewManager() *Manager {
	return &Manager{
		models: make(map[config.ModelID]model.ToolCallingChatModel),
	}
}

func (m *Manager) InitModel(modelConfigs []config.ModelConfig) error {
	for _, cfg := range modelConfigs {
		modelType := config.ModelID(cfg.ModelID)

		// 根据模型类型和配置创建对应的模型实例
		chatModel, err := m.createModel(modelType, &cfg)
		if err != nil {
			return err
		}

		m.models[modelType] = chatModel
	}
	return nil
}

// createModel 根据模型类型和配置创建模型实例
// Claude 的模型现在兼容 OpenAI 的通用接口，因此可以使用 OpenAI 的客户端库。
func (m *Manager) createModel(modelType config.ModelID, cfg *config.ModelConfig) (model.ToolCallingChatModel, error) {
	ctx := context.Background()

	switch modelType {
	case config.DeepSeekR1:
		return openai.NewChatModel(ctx, &openai.ChatModelConfig{
			APIKey:    cfg.APIKey,
			BaseURL:   cfg.BaseURL,
			Model:     cfg.Model,
			MaxTokens: &cfg.MaxTokens,
		})

	case config.Claude35Sonnet:
		return openai.NewChatModel(ctx, &openai.ChatModelConfig{
			APIKey:    cfg.APIKey,
			BaseURL:   cfg.BaseURL,
			Model:     cfg.Model,
			MaxTokens: &cfg.MaxTokens,
		})

	case config.Claude37Sonnet:
		return openai.NewChatModel(ctx, &openai.ChatModelConfig{
			APIKey:    cfg.APIKey,
			BaseURL:   cfg.BaseURL,
			Model:     cfg.Model,
			MaxTokens: &cfg.MaxTokens,
		})

	case config.Claude4Sonnet:
		return openai.NewChatModel(ctx, &openai.ChatModelConfig{
			APIKey:    cfg.APIKey,
			BaseURL:   cfg.BaseURL,
			Model:     cfg.Model,
			MaxTokens: &cfg.MaxTokens,
		})

	case config.Qwen3:
		return openai.NewChatModel(ctx, &openai.ChatModelConfig{
			APIKey:    cfg.APIKey,
			BaseURL:   cfg.BaseURL,
			Model:     cfg.Model,
			MaxTokens: &cfg.MaxTokens,
		})

	default:
		return nil, fmt.Errorf("unsupported model type: %s", modelType)
	}
}

func (m *Manager) GetModel(modelType config.ModelID) (model.ToolCallingChatModel, error) {
	if chatModel, exists := m.models[modelType]; exists {
		return chatModel, nil
	}
	return nil, errors.New("model not found or not initialized")
}
