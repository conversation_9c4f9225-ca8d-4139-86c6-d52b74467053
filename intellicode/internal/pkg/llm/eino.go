package llm

import (
	"context"
	"errors"
	"io"

	"github.com/cloudwego/eino/schema"
)

// StreamingToolCallChecker 主要提供给流式输出的使用场景中检查是否有 ToolCall
func StreamingToolCallChecker(ctx context.Context, sr *schema.StreamReader[*schema.Message]) (bool, error) {

	defer sr.Close()
	for {
		msg, err := sr.Recv()
		if err != nil {
			if errors.Is(err, io.EOF) {
				// finish
				break
			}

			return false, err
		}

		if len(msg.ToolCalls) > 0 {
			return true, nil
		}
	}

	return false, nil
}
