package llm

// ExtractJsonFromString 从字符串中提取 JSON部分内容。
// LLM 返回信息可能会在 JSON 之前或之后包含其他文本。
// 需要去除的情况：
//   - json 前后的空行
//   - ```json```
//   - ``````
import (
	"errors"
	"regexp"
	"strings"
)

func ExtractJsonFromString(input string) (string, error) {
	// 去除 markdown 代码块标记
	re := regexp.MustCompile("(?s)```(?:json)?\\s*(.*?)\\s*```")
	matches := re.FindStringSubmatch(input)
	var content string
	if len(matches) > 1 && matches[1] != "" {
		content = matches[1]
	} else {
		content = input
	}
	// 去除前后空行
	content = strings.TrimSpace(content)
	// 尝试找到第一个 '{' 和最后一个 '}'
	start := strings.Index(content, "{")
	end := strings.LastIndex(content, "}")
	if start == -1 || end == -1 || end < start {
		return "", errors.New("no valid JSON object found")
	}
	jsonStr := content[start : end+1]
	return jsonStr, nil
}
