package prompt

import (
	"context"
	"intellicode/internal/pkg/config"
	"strings"

	"github.com/pkg/errors"
)

type TemplateSet map[string]string // 模板集合 fileName: fileContent

const TemplateDir = "internal/pkg/prompt/templates"

type TemplateManager interface {
	// 加载所有的模板
	Load(ctx context.Context, env config.Environment) error
	// Get 通过 name 查询模板文件内容，name 是 prompt 文件名。 {name}.prompt
	Get(ctx context.Context, name string) (string, error)
}

// localFSManager 是基于本地文件系统的 Prompt 模板实现。后续可以拓展 langfuse 的实现。
type localFSManager struct {
	templates TemplateSet
}

// NewTemplateManager 创建新的模板管理器
func NewTemplateManager() TemplateManager {
	return &localFSManager{
		templates: make(TemplateSet),
	}
}

// Load 根据环境加载模板
func (tm *localFSManager) Load(ctx context.Context, env config.Environment) error {
	var templateSet TemplateSet
	var err error

	switch env {
	case config.EnvProduction:
		// 生产环境从嵌入文件系统加载
		templateSet, err = loadTemplateFromEmbed()
		if err != nil {
			return errors.Wrap(err, "failed to load templates from embed")
		}
	case config.EnvDevelopment:
		// 开发环境通过代码仓库文件系统加载
		templateSet, err = loadFileSystemTemplate(TemplateDir)
		if err != nil {
			return errors.Wrap(err, "failed to load templates from filesystem")
		}
	default:
		return errors.Errorf("unsupported environment: %s", env)
	}

	tm.templates = templateSet
	return nil
}

// Get 通过 name 查询模板文件内容
func (tm *localFSManager) Get(ctx context.Context, name string) (string, error) {
	if tm.templates == nil {
		return "", errors.New("templates not loaded, call Load() first")
	}

	if !strings.HasSuffix(name, ".prompt") {
		name = name + ".prompt"
	}

	templateContent, exists := tm.templates[name]
	if !exists {
		return "", errors.Errorf("template not found: %s", name)
	}

	return templateContent, nil
}
