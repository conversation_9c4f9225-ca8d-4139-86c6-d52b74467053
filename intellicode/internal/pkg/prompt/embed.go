package prompt

import (
	"embed"
	"io/fs"
	"path/filepath"
	"strings"

	"github.com/pkg/errors"
)

//go:embed templates/*.prompt
var embeddedTemplates embed.FS

// loadTemplateFromEmbed 从嵌入文件系统加载模板（生产环境）
func loadTemplateFromEmbed() (TemplateSet, error) {
	promptMap := make(map[string]string)

	// 使用 fs.Glob 匹配所有 .prompt 文件
	matches, err := fs.Glob(embeddedTemplates, "templates/*.prompt")
	if err != nil {
		return nil, errors.Wrap(err, "failed to glob embedded templates")
	}

	if len(matches) == 0 {
		return nil, errors.New("no *.prompt file found in embedded templates")
	}

	for _, path := range matches {
		content, err := embeddedTemplates.ReadFile(path)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to read embedded file: %s", path)
		}
		promptMap[filepath.Base(path)] = string(content)
	}

	return promptMap, nil
}

// getEmbeddedTemplate 从嵌入文件系统获取单个模板
func getEmbeddedTemplate(name string) (string, error) {
	if !strings.HasSuffix(name, ".prompt") {
		name += ".prompt"
	}

	filePath := filepath.Join("templates", name)
	content, err := embeddedTemplates.ReadFile(filePath)
	if err != nil {
		return "", errors.Wrapf(err, "failed to read embedded template: %s", name)
	}

	return string(content), nil
}

// listEmbeddedTemplates 列出所有嵌入的模板文件名
func listEmbeddedTemplates() ([]string, error) {
	var templates []string

	err := fs.WalkDir(embeddedTemplates, "templates", func(path string, d fs.DirEntry, err error) error {
		if err != nil {
			return errors.Wrap(err, "failed to walk embed directory")
		}

		if !d.IsDir() && strings.HasSuffix(path, ".prompt") {
			templates = append(templates, filepath.Base(path))
		}
		return nil
	})

	if err != nil {
		return nil, errors.Wrap(err, "failed to list embedded templates")
	}

	return templates, nil
}

// hasEmbeddedTemplate 检查指定的模板是否存在于嵌入文件系统中
func hasEmbeddedTemplate(name string) bool {
	if !strings.HasSuffix(name, ".prompt") {
		name += ".prompt"
	}

	filePath := filepath.Join("templates", name)
	_, err := embeddedTemplates.ReadFile(filePath)
	return err == nil
}
