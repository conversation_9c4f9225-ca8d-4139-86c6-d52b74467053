package prompt

import (
	"os"
	"path/filepath"

	"github.com/pkg/errors"
)

// loadFileSystemTemplate load *.prompt file in dir and return map[string]string (file_name:content)
// 这个方法应该是在开发环境中使用的
func loadFileSystemTemplate(dir string) (TemplateSet, error) {
	promptMap := make(map[string]string)

	files, err := filepath.Glob(filepath.Join(dir, "*.prompt"))
	if err != nil {
		return nil, errors.Wrapf(err, "failed to glob templates in directory: %s", dir)
	}

	if len(files) == 0 {
		return nil, errors.Errorf("no *.prompt file found in directory: %s", dir)
	}

	for _, file := range files {
		content, err := os.ReadFile(file)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to read template file: %s", file)
		}
		promptMap[filepath.Base(file)] = string(content)
	}
	return promptMap, nil
}
