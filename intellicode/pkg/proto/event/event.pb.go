// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v5.29.3
// source: pkg/proto/event/event.proto

package event

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// EventType 定义事件类型枚举
type EventType int32

const (
	EventType_EVENT_TYPE_UNSPECIFIED EventType = 0
	// EventTypeToolStart 工具开始执行事件
	EventType_EVENT_TYPE_TOOL_START EventType = 1
	// EventTypeToolEnd 工具执行完成事件
	EventType_EVENT_TYPE_TOOL_END EventType = 2
	// EventTypeAssistant LLM 流式响应事件
	EventType_EVENT_TYPE_ASSISTANT EventType = 3
	// EventTypeSuggestions 修复建议事件
	EventType_EVENT_TYPE_SUGGESTIONS EventType = 4
	// EventTypeSessionStart 会话开始事件
	EventType_EVENT_TYPE_SESSION_START EventType = 5
	// EventTypeSessionEnd 会话结束事件
	EventType_EVENT_TYPE_SESSION_END EventType = 6
)

// Enum value maps for EventType.
var (
	EventType_name = map[int32]string{
		0: "EVENT_TYPE_UNSPECIFIED",
		1: "EVENT_TYPE_TOOL_START",
		2: "EVENT_TYPE_TOOL_END",
		3: "EVENT_TYPE_ASSISTANT",
		4: "EVENT_TYPE_SUGGESTIONS",
		5: "EVENT_TYPE_SESSION_START",
		6: "EVENT_TYPE_SESSION_END",
	}
	EventType_value = map[string]int32{
		"EVENT_TYPE_UNSPECIFIED":   0,
		"EVENT_TYPE_TOOL_START":    1,
		"EVENT_TYPE_TOOL_END":      2,
		"EVENT_TYPE_ASSISTANT":     3,
		"EVENT_TYPE_SUGGESTIONS":   4,
		"EVENT_TYPE_SESSION_START": 5,
		"EVENT_TYPE_SESSION_END":   6,
	}
)

func (x EventType) Enum() *EventType {
	p := new(EventType)
	*p = x
	return p
}

func (x EventType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EventType) Descriptor() protoreflect.EnumDescriptor {
	return file_pkg_proto_event_event_proto_enumTypes[0].Descriptor()
}

func (EventType) Type() protoreflect.EnumType {
	return &file_pkg_proto_event_event_proto_enumTypes[0]
}

func (x EventType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EventType.Descriptor instead.
func (EventType) EnumDescriptor() ([]byte, []int) {
	return file_pkg_proto_event_event_proto_rawDescGZIP(), []int{0}
}

// ToolStatus 定义工具状态枚举
type ToolStatus int32

const (
	ToolStatus_TOOL_STATUS_UNSPECIFIED ToolStatus = 0
	// 工具正在运行
	ToolStatus_TOOL_STATUS_RUNNING ToolStatus = 1
	// 工具执行成功
	ToolStatus_TOOL_STATUS_SUCCESS ToolStatus = 2
	// 工具执行出错
	ToolStatus_TOOL_STATUS_ERROR ToolStatus = 3
)

// Enum value maps for ToolStatus.
var (
	ToolStatus_name = map[int32]string{
		0: "TOOL_STATUS_UNSPECIFIED",
		1: "TOOL_STATUS_RUNNING",
		2: "TOOL_STATUS_SUCCESS",
		3: "TOOL_STATUS_ERROR",
	}
	ToolStatus_value = map[string]int32{
		"TOOL_STATUS_UNSPECIFIED": 0,
		"TOOL_STATUS_RUNNING":     1,
		"TOOL_STATUS_SUCCESS":     2,
		"TOOL_STATUS_ERROR":       3,
	}
)

func (x ToolStatus) Enum() *ToolStatus {
	p := new(ToolStatus)
	*p = x
	return p
}

func (x ToolStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ToolStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_pkg_proto_event_event_proto_enumTypes[1].Descriptor()
}

func (ToolStatus) Type() protoreflect.EnumType {
	return &file_pkg_proto_event_event_proto_enumTypes[1]
}

func (x ToolStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ToolStatus.Descriptor instead.
func (ToolStatus) EnumDescriptor() ([]byte, []int) {
	return file_pkg_proto_event_event_proto_rawDescGZIP(), []int{1}
}

// Event 基础事件结构
type Event struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type EventType `protobuf:"varint,1,opt,name=type,proto3,enum=intellicode.event.v1.EventType" json:"type,omitempty"`
	// Types that are assignable to Data:
	//
	//	*Event_ToolStart
	//	*Event_ToolEnd
	//	*Event_Assistant
	//	*Event_Suggestions
	//	*Event_SessionStart
	//	*Event_SessionEnd
	Data isEvent_Data `protobuf_oneof:"data"`
}

func (x *Event) Reset() {
	*x = Event{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_proto_event_event_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Event) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Event) ProtoMessage() {}

func (x *Event) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_event_event_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Event.ProtoReflect.Descriptor instead.
func (*Event) Descriptor() ([]byte, []int) {
	return file_pkg_proto_event_event_proto_rawDescGZIP(), []int{0}
}

func (x *Event) GetType() EventType {
	if x != nil {
		return x.Type
	}
	return EventType_EVENT_TYPE_UNSPECIFIED
}

func (m *Event) GetData() isEvent_Data {
	if m != nil {
		return m.Data
	}
	return nil
}

func (x *Event) GetToolStart() *ToolStartData {
	if x, ok := x.GetData().(*Event_ToolStart); ok {
		return x.ToolStart
	}
	return nil
}

func (x *Event) GetToolEnd() *ToolEndData {
	if x, ok := x.GetData().(*Event_ToolEnd); ok {
		return x.ToolEnd
	}
	return nil
}

func (x *Event) GetAssistant() *AssistantData {
	if x, ok := x.GetData().(*Event_Assistant); ok {
		return x.Assistant
	}
	return nil
}

func (x *Event) GetSuggestions() *SuggestionsData {
	if x, ok := x.GetData().(*Event_Suggestions); ok {
		return x.Suggestions
	}
	return nil
}

func (x *Event) GetSessionStart() *SessionStartData {
	if x, ok := x.GetData().(*Event_SessionStart); ok {
		return x.SessionStart
	}
	return nil
}

func (x *Event) GetSessionEnd() *SessionEndData {
	if x, ok := x.GetData().(*Event_SessionEnd); ok {
		return x.SessionEnd
	}
	return nil
}

type isEvent_Data interface {
	isEvent_Data()
}

type Event_ToolStart struct {
	ToolStart *ToolStartData `protobuf:"bytes,2,opt,name=tool_start,json=toolStart,proto3,oneof"`
}

type Event_ToolEnd struct {
	ToolEnd *ToolEndData `protobuf:"bytes,3,opt,name=tool_end,json=toolEnd,proto3,oneof"`
}

type Event_Assistant struct {
	Assistant *AssistantData `protobuf:"bytes,4,opt,name=assistant,proto3,oneof"`
}

type Event_Suggestions struct {
	Suggestions *SuggestionsData `protobuf:"bytes,5,opt,name=suggestions,proto3,oneof"`
}

type Event_SessionStart struct {
	SessionStart *SessionStartData `protobuf:"bytes,6,opt,name=session_start,json=sessionStart,proto3,oneof"`
}

type Event_SessionEnd struct {
	SessionEnd *SessionEndData `protobuf:"bytes,7,opt,name=session_end,json=sessionEnd,proto3,oneof"`
}

func (*Event_ToolStart) isEvent_Data() {}

func (*Event_ToolEnd) isEvent_Data() {}

func (*Event_Assistant) isEvent_Data() {}

func (*Event_Suggestions) isEvent_Data() {}

func (*Event_SessionStart) isEvent_Data() {}

func (*Event_SessionEnd) isEvent_Data() {}

// ToolStartData 工具开始事件数据
type ToolStartData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name   string     `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Input  string     `protobuf:"bytes,2,opt,name=input,proto3" json:"input,omitempty"`
	Status ToolStatus `protobuf:"varint,3,opt,name=status,proto3,enum=intellicode.event.v1.ToolStatus" json:"status,omitempty"`
}

func (x *ToolStartData) Reset() {
	*x = ToolStartData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_proto_event_event_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ToolStartData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ToolStartData) ProtoMessage() {}

func (x *ToolStartData) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_event_event_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ToolStartData.ProtoReflect.Descriptor instead.
func (*ToolStartData) Descriptor() ([]byte, []int) {
	return file_pkg_proto_event_event_proto_rawDescGZIP(), []int{1}
}

func (x *ToolStartData) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ToolStartData) GetInput() string {
	if x != nil {
		return x.Input
	}
	return ""
}

func (x *ToolStartData) GetStatus() ToolStatus {
	if x != nil {
		return x.Status
	}
	return ToolStatus_TOOL_STATUS_UNSPECIFIED
}

// ToolEndData 工具结束事件数据
type ToolEndData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name   string     `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Input  string     `protobuf:"bytes,2,opt,name=input,proto3" json:"input,omitempty"`
	Output string     `protobuf:"bytes,3,opt,name=output,proto3" json:"output,omitempty"`
	Status ToolStatus `protobuf:"varint,4,opt,name=status,proto3,enum=intellicode.event.v1.ToolStatus" json:"status,omitempty"`
}

func (x *ToolEndData) Reset() {
	*x = ToolEndData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_proto_event_event_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ToolEndData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ToolEndData) ProtoMessage() {}

func (x *ToolEndData) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_event_event_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ToolEndData.ProtoReflect.Descriptor instead.
func (*ToolEndData) Descriptor() ([]byte, []int) {
	return file_pkg_proto_event_event_proto_rawDescGZIP(), []int{2}
}

func (x *ToolEndData) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ToolEndData) GetInput() string {
	if x != nil {
		return x.Input
	}
	return ""
}

func (x *ToolEndData) GetOutput() string {
	if x != nil {
		return x.Output
	}
	return ""
}

func (x *ToolEndData) GetStatus() ToolStatus {
	if x != nil {
		return x.Status
	}
	return ToolStatus_TOOL_STATUS_UNSPECIFIED
}

// AssistantData LLM 响应事件数据
type AssistantData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Chunk string `protobuf:"bytes,1,opt,name=chunk,proto3" json:"chunk,omitempty"`
}

func (x *AssistantData) Reset() {
	*x = AssistantData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_proto_event_event_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AssistantData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssistantData) ProtoMessage() {}

func (x *AssistantData) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_event_event_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssistantData.ProtoReflect.Descriptor instead.
func (*AssistantData) Descriptor() ([]byte, []int) {
	return file_pkg_proto_event_event_proto_rawDescGZIP(), []int{3}
}

func (x *AssistantData) GetChunk() string {
	if x != nil {
		return x.Chunk
	}
	return ""
}

// SuggestionsData 修复建议事件数据
type SuggestionsData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Suggestions []*Suggestion `protobuf:"bytes,1,rep,name=suggestions,proto3" json:"suggestions,omitempty"`
}

func (x *SuggestionsData) Reset() {
	*x = SuggestionsData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_proto_event_event_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SuggestionsData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SuggestionsData) ProtoMessage() {}

func (x *SuggestionsData) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_event_event_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SuggestionsData.ProtoReflect.Descriptor instead.
func (*SuggestionsData) Descriptor() ([]byte, []int) {
	return file_pkg_proto_event_event_proto_rawDescGZIP(), []int{4}
}

func (x *SuggestionsData) GetSuggestions() []*Suggestion {
	if x != nil {
		return x.Suggestions
	}
	return nil
}

// Suggestion 修复建议
type Suggestion struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int32  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Category    string `protobuf:"bytes,2,opt,name=category,proto3" json:"category,omitempty"`
	Level       string `protobuf:"bytes,3,opt,name=level,proto3" json:"level,omitempty"`
	FilePath    string `protobuf:"bytes,4,opt,name=file_path,json=filePath,proto3" json:"file_path,omitempty"`
	StartLine   int32  `protobuf:"varint,5,opt,name=start_line,json=startLine,proto3" json:"start_line,omitempty"`
	EndLine     int32  `protobuf:"varint,6,opt,name=end_line,json=endLine,proto3" json:"end_line,omitempty"`
	Description string `protobuf:"bytes,7,opt,name=description,proto3" json:"description,omitempty"`
	OldString   string `protobuf:"bytes,8,opt,name=old_string,json=oldString,proto3" json:"old_string,omitempty"`
	NewString   string `protobuf:"bytes,9,opt,name=new_string,json=newString,proto3" json:"new_string,omitempty"`
}

func (x *Suggestion) Reset() {
	*x = Suggestion{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_proto_event_event_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Suggestion) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Suggestion) ProtoMessage() {}

func (x *Suggestion) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_event_event_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Suggestion.ProtoReflect.Descriptor instead.
func (*Suggestion) Descriptor() ([]byte, []int) {
	return file_pkg_proto_event_event_proto_rawDescGZIP(), []int{5}
}

func (x *Suggestion) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Suggestion) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

func (x *Suggestion) GetLevel() string {
	if x != nil {
		return x.Level
	}
	return ""
}

func (x *Suggestion) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

func (x *Suggestion) GetStartLine() int32 {
	if x != nil {
		return x.StartLine
	}
	return 0
}

func (x *Suggestion) GetEndLine() int32 {
	if x != nil {
		return x.EndLine
	}
	return 0
}

func (x *Suggestion) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Suggestion) GetOldString() string {
	if x != nil {
		return x.OldString
	}
	return ""
}

func (x *Suggestion) GetNewString() string {
	if x != nil {
		return x.NewString
	}
	return ""
}

// SessionStartData 会话开始事件数据
type SessionStartData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SessionId   string   `protobuf:"bytes,1,opt,name=session_id,json=sessionId,proto3" json:"session_id,omitempty"`
	ReviewFiles []string `protobuf:"bytes,2,rep,name=review_files,json=reviewFiles,proto3" json:"review_files,omitempty"`
}

func (x *SessionStartData) Reset() {
	*x = SessionStartData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_proto_event_event_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SessionStartData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SessionStartData) ProtoMessage() {}

func (x *SessionStartData) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_event_event_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SessionStartData.ProtoReflect.Descriptor instead.
func (*SessionStartData) Descriptor() ([]byte, []int) {
	return file_pkg_proto_event_event_proto_rawDescGZIP(), []int{6}
}

func (x *SessionStartData) GetSessionId() string {
	if x != nil {
		return x.SessionId
	}
	return ""
}

func (x *SessionStartData) GetReviewFiles() []string {
	if x != nil {
		return x.ReviewFiles
	}
	return nil
}

// SessionEndData 会话结束事件数据
type SessionEndData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SessionId string `protobuf:"bytes,1,opt,name=session_id,json=sessionId,proto3" json:"session_id,omitempty"`
	Success   bool   `protobuf:"varint,2,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *SessionEndData) Reset() {
	*x = SessionEndData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_proto_event_event_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SessionEndData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SessionEndData) ProtoMessage() {}

func (x *SessionEndData) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_event_event_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SessionEndData.ProtoReflect.Descriptor instead.
func (*SessionEndData) Descriptor() ([]byte, []int) {
	return file_pkg_proto_event_event_proto_rawDescGZIP(), []int{7}
}

func (x *SessionEndData) GetSessionId() string {
	if x != nil {
		return x.SessionId
	}
	return ""
}

func (x *SessionEndData) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

var File_pkg_proto_event_event_proto protoreflect.FileDescriptor

var file_pkg_proto_event_event_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x70, 0x6b, 0x67, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x65, 0x76, 0x65, 0x6e,
	0x74, 0x2f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x14, 0x69,
	0x6e, 0x74, 0x65, 0x6c, 0x6c, 0x69, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x22, 0xf2, 0x03, 0x0a, 0x05, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x33, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x69, 0x6e,
	0x74, 0x65, 0x6c, 0x6c, 0x69, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x12, 0x44, 0x0a, 0x0a, 0x74, 0x6f, 0x6f, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x6c, 0x6c, 0x69,
	0x63, 0x6f, 0x64, 0x65, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x6f,
	0x6f, 0x6c, 0x53, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x09, 0x74,
	0x6f, 0x6f, 0x6c, 0x53, 0x74, 0x61, 0x72, 0x74, 0x12, 0x3e, 0x0a, 0x08, 0x74, 0x6f, 0x6f, 0x6c,
	0x5f, 0x65, 0x6e, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x69, 0x6e, 0x74,
	0x65, 0x6c, 0x6c, 0x69, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x54, 0x6f, 0x6f, 0x6c, 0x45, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52,
	0x07, 0x74, 0x6f, 0x6f, 0x6c, 0x45, 0x6e, 0x64, 0x12, 0x43, 0x0a, 0x09, 0x61, 0x73, 0x73, 0x69,
	0x73, 0x74, 0x61, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x69, 0x6e,
	0x74, 0x65, 0x6c, 0x6c, 0x69, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x41, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61,
	0x48, 0x00, 0x52, 0x09, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x12, 0x49, 0x0a,
	0x0b, 0x73, 0x75, 0x67, 0x67, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x25, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x6c, 0x6c, 0x69, 0x63, 0x6f, 0x64, 0x65,
	0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75, 0x67, 0x67, 0x65, 0x73,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x0b, 0x73, 0x75, 0x67,
	0x67, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x4d, 0x0a, 0x0d, 0x73, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x26, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x6c, 0x6c, 0x69, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x65, 0x76,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x74,
	0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x0c, 0x73, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x12, 0x47, 0x0a, 0x0b, 0x73, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x5f, 0x65, 0x6e, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x69,
	0x6e, 0x74, 0x65, 0x6c, 0x6c, 0x69, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x45, 0x6e, 0x64, 0x44, 0x61,
	0x74, 0x61, 0x48, 0x00, 0x52, 0x0a, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x45, 0x6e, 0x64,
	0x42, 0x06, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x73, 0x0a, 0x0d, 0x54, 0x6f, 0x6f, 0x6c,
	0x53, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x6e,
	0x70, 0x75, 0x74, 0x12, 0x38, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x6c, 0x6c, 0x69, 0x63, 0x6f, 0x64,
	0x65, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x6f, 0x6f, 0x6c, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x89, 0x01,
	0x0a, 0x0b, 0x54, 0x6f, 0x6f, 0x6c, 0x45, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x75, 0x74, 0x70, 0x75,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x12,
	0x38, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x20, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x6c, 0x6c, 0x69, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x65, 0x76,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x6f, 0x6f, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x25, 0x0a, 0x0d, 0x41, 0x73, 0x73,
	0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x68,
	0x75, 0x6e, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x68, 0x75, 0x6e, 0x6b,
	0x22, 0x55, 0x0a, 0x0f, 0x53, 0x75, 0x67, 0x67, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x42, 0x0a, 0x0b, 0x73, 0x75, 0x67, 0x67, 0x65, 0x73, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x6c,
	0x6c, 0x69, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x75, 0x67, 0x67, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x73, 0x75, 0x67, 0x67,
	0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x85, 0x02, 0x0a, 0x0a, 0x53, 0x75, 0x67, 0x67,
	0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65,
	0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c,
	0x65, 0x50, 0x61, 0x74, 0x68, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x6c,
	0x69, 0x6e, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x4c, 0x69, 0x6e, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x6c, 0x69, 0x6e, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x4c, 0x69, 0x6e, 0x65, 0x12,
	0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6f, 0x6c, 0x64, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x12, 0x1d, 0x0a, 0x0a, 0x6e, 0x65, 0x77, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x65, 0x77, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x22,
	0x54, 0x0a, 0x10, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x66, 0x69, 0x6c,
	0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x46, 0x69, 0x6c, 0x65, 0x73, 0x22, 0x49, 0x0a, 0x0e, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x45, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x2a, 0xcb, 0x01, 0x0a, 0x09, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a,
	0x0a, 0x16, 0x45, 0x56, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x19, 0x0a, 0x15, 0x45, 0x56,
	0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x4f, 0x4f, 0x4c, 0x5f, 0x53, 0x54,
	0x41, 0x52, 0x54, 0x10, 0x01, 0x12, 0x17, 0x0a, 0x13, 0x45, 0x56, 0x45, 0x4e, 0x54, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x54, 0x4f, 0x4f, 0x4c, 0x5f, 0x45, 0x4e, 0x44, 0x10, 0x02, 0x12, 0x18,
	0x0a, 0x14, 0x45, 0x56, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x53, 0x53,
	0x49, 0x53, 0x54, 0x41, 0x4e, 0x54, 0x10, 0x03, 0x12, 0x1a, 0x0a, 0x16, 0x45, 0x56, 0x45, 0x4e,
	0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x55, 0x47, 0x47, 0x45, 0x53, 0x54, 0x49, 0x4f,
	0x4e, 0x53, 0x10, 0x04, 0x12, 0x1c, 0x0a, 0x18, 0x45, 0x56, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x53, 0x45, 0x53, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x52, 0x54,
	0x10, 0x05, 0x12, 0x1a, 0x0a, 0x16, 0x45, 0x56, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x53, 0x45, 0x53, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x45, 0x4e, 0x44, 0x10, 0x06, 0x2a, 0x72,
	0x0a, 0x0a, 0x54, 0x6f, 0x6f, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1b, 0x0a, 0x17,
	0x54, 0x4f, 0x4f, 0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13, 0x54, 0x4f, 0x4f,
	0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x52, 0x55, 0x4e, 0x4e, 0x49, 0x4e, 0x47,
	0x10, 0x01, 0x12, 0x17, 0x0a, 0x13, 0x54, 0x4f, 0x4f, 0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x02, 0x12, 0x15, 0x0a, 0x11, 0x54,
	0x4f, 0x4f, 0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52,
	0x10, 0x03, 0x42, 0x1d, 0x5a, 0x1b, 0x69, 0x6e, 0x74, 0x65, 0x6c, 0x6c, 0x69, 0x63, 0x6f, 0x64,
	0x65, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x65, 0x76, 0x65, 0x6e,
	0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pkg_proto_event_event_proto_rawDescOnce sync.Once
	file_pkg_proto_event_event_proto_rawDescData = file_pkg_proto_event_event_proto_rawDesc
)

func file_pkg_proto_event_event_proto_rawDescGZIP() []byte {
	file_pkg_proto_event_event_proto_rawDescOnce.Do(func() {
		file_pkg_proto_event_event_proto_rawDescData = protoimpl.X.CompressGZIP(file_pkg_proto_event_event_proto_rawDescData)
	})
	return file_pkg_proto_event_event_proto_rawDescData
}

var file_pkg_proto_event_event_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_pkg_proto_event_event_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_pkg_proto_event_event_proto_goTypes = []interface{}{
	(EventType)(0),           // 0: intellicode.event.v1.EventType
	(ToolStatus)(0),          // 1: intellicode.event.v1.ToolStatus
	(*Event)(nil),            // 2: intellicode.event.v1.Event
	(*ToolStartData)(nil),    // 3: intellicode.event.v1.ToolStartData
	(*ToolEndData)(nil),      // 4: intellicode.event.v1.ToolEndData
	(*AssistantData)(nil),    // 5: intellicode.event.v1.AssistantData
	(*SuggestionsData)(nil),  // 6: intellicode.event.v1.SuggestionsData
	(*Suggestion)(nil),       // 7: intellicode.event.v1.Suggestion
	(*SessionStartData)(nil), // 8: intellicode.event.v1.SessionStartData
	(*SessionEndData)(nil),   // 9: intellicode.event.v1.SessionEndData
}
var file_pkg_proto_event_event_proto_depIdxs = []int32{
	0,  // 0: intellicode.event.v1.Event.type:type_name -> intellicode.event.v1.EventType
	3,  // 1: intellicode.event.v1.Event.tool_start:type_name -> intellicode.event.v1.ToolStartData
	4,  // 2: intellicode.event.v1.Event.tool_end:type_name -> intellicode.event.v1.ToolEndData
	5,  // 3: intellicode.event.v1.Event.assistant:type_name -> intellicode.event.v1.AssistantData
	6,  // 4: intellicode.event.v1.Event.suggestions:type_name -> intellicode.event.v1.SuggestionsData
	8,  // 5: intellicode.event.v1.Event.session_start:type_name -> intellicode.event.v1.SessionStartData
	9,  // 6: intellicode.event.v1.Event.session_end:type_name -> intellicode.event.v1.SessionEndData
	1,  // 7: intellicode.event.v1.ToolStartData.status:type_name -> intellicode.event.v1.ToolStatus
	1,  // 8: intellicode.event.v1.ToolEndData.status:type_name -> intellicode.event.v1.ToolStatus
	7,  // 9: intellicode.event.v1.SuggestionsData.suggestions:type_name -> intellicode.event.v1.Suggestion
	10, // [10:10] is the sub-list for method output_type
	10, // [10:10] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_pkg_proto_event_event_proto_init() }
func file_pkg_proto_event_event_proto_init() {
	if File_pkg_proto_event_event_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pkg_proto_event_event_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Event); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_proto_event_event_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ToolStartData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_proto_event_event_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ToolEndData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_proto_event_event_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AssistantData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_proto_event_event_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SuggestionsData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_proto_event_event_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Suggestion); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_proto_event_event_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SessionStartData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_proto_event_event_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SessionEndData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_pkg_proto_event_event_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*Event_ToolStart)(nil),
		(*Event_ToolEnd)(nil),
		(*Event_Assistant)(nil),
		(*Event_Suggestions)(nil),
		(*Event_SessionStart)(nil),
		(*Event_SessionEnd)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pkg_proto_event_event_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pkg_proto_event_event_proto_goTypes,
		DependencyIndexes: file_pkg_proto_event_event_proto_depIdxs,
		EnumInfos:         file_pkg_proto_event_event_proto_enumTypes,
		MessageInfos:      file_pkg_proto_event_event_proto_msgTypes,
	}.Build()
	File_pkg_proto_event_event_proto = out.File
	file_pkg_proto_event_event_proto_rawDesc = nil
	file_pkg_proto_event_event_proto_goTypes = nil
	file_pkg_proto_event_event_proto_depIdxs = nil
}
