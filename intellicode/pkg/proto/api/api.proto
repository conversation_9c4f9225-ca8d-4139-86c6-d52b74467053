syntax = "proto3";

package intellicode.api.v1;

option go_package = "intellicode/pkg/proto/api";

import "google/protobuf/descriptor.proto";
import "google/api/annotations.proto";
import "pkg/proto/event/event.proto";

// 1. 声明扩展字段，附加到 MethodOptions（RPC 方法的 option）
extend google.protobuf.MethodOptions {
  bool sse_enabled = 50001;
}

enum IDE {
  IDE_UNSPECIFIED = 0;
  IDE_JETBRAINS = 1;
  IDE_VSCODE = 2;
}

// ReviewRequest 代码审查请求
message ReviewRequest {
  // 仓库路径，绝对路径
  string repo_path = 1;
  // 需要审查的文件列表，如果为空则审查所有变更文件
  repeated string review_files = 2;
  IDE ide = 3;
}

// ReviewService 代码审查服务接口
service ReviewService {
  // Review 执行代码审查，返回 SSE 事件流
  // 使用 HTTP POST /api/review
  // 支持 Server-Sent Events (SSE) 流式响应
  rpc StreamReview(ReviewRequest) returns (intellicode.event.v1.Event) {
    option (sse_enabled) = true;
    option (google.api.http) = {
      post: "/api/review"
    };
  };

  // 查看 Review 记录列表 对应 db
//   rpc ListReviewRecords(ListRecordReq) returns (ListRecordResp);

  // 查看 Review 记录详情 对应本地 Session 文件
//   rpc GetReviewSession();

  // 通过 Suggestion event 触发查询
//   rpc ListReviewSuggestions();
//   rpc SuggestionFeedback();
}
