// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v5.29.3
// source: pkg/proto/api/api.proto

package api

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	descriptorpb "google.golang.org/protobuf/types/descriptorpb"
	event "intellicode/pkg/proto/event"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type IDE int32

const (
	IDE_IDE_UNSPECIFIED IDE = 0
	IDE_IDE_JETBRAINS   IDE = 1
	IDE_IDE_VSCODE      IDE = 2
)

// Enum value maps for IDE.
var (
	IDE_name = map[int32]string{
		0: "IDE_UNSPECIFIED",
		1: "IDE_JETBRAINS",
		2: "IDE_VSCODE",
	}
	IDE_value = map[string]int32{
		"IDE_UNSPECIFIED": 0,
		"IDE_JETBRAINS":   1,
		"IDE_VSCODE":      2,
	}
)

func (x IDE) Enum() *IDE {
	p := new(IDE)
	*p = x
	return p
}

func (x IDE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (IDE) Descriptor() protoreflect.EnumDescriptor {
	return file_pkg_proto_api_api_proto_enumTypes[0].Descriptor()
}

func (IDE) Type() protoreflect.EnumType {
	return &file_pkg_proto_api_api_proto_enumTypes[0]
}

func (x IDE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use IDE.Descriptor instead.
func (IDE) EnumDescriptor() ([]byte, []int) {
	return file_pkg_proto_api_api_proto_rawDescGZIP(), []int{0}
}

// ReviewRequest 代码审查请求
type ReviewRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 仓库路径，绝对路径
	RepoPath string `protobuf:"bytes,1,opt,name=repo_path,json=repoPath,proto3" json:"repo_path,omitempty"`
	// 需要审查的文件列表，如果为空则审查所有变更文件
	ReviewFiles []string `protobuf:"bytes,2,rep,name=review_files,json=reviewFiles,proto3" json:"review_files,omitempty"`
	Ide         IDE      `protobuf:"varint,3,opt,name=ide,proto3,enum=intellicode.api.v1.IDE" json:"ide,omitempty"`
}

func (x *ReviewRequest) Reset() {
	*x = ReviewRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_proto_api_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReviewRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReviewRequest) ProtoMessage() {}

func (x *ReviewRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_api_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReviewRequest.ProtoReflect.Descriptor instead.
func (*ReviewRequest) Descriptor() ([]byte, []int) {
	return file_pkg_proto_api_api_proto_rawDescGZIP(), []int{0}
}

func (x *ReviewRequest) GetRepoPath() string {
	if x != nil {
		return x.RepoPath
	}
	return ""
}

func (x *ReviewRequest) GetReviewFiles() []string {
	if x != nil {
		return x.ReviewFiles
	}
	return nil
}

func (x *ReviewRequest) GetIde() IDE {
	if x != nil {
		return x.Ide
	}
	return IDE_IDE_UNSPECIFIED
}

var file_pkg_proto_api_api_proto_extTypes = []protoimpl.ExtensionInfo{
	{
		ExtendedType:  (*descriptorpb.MethodOptions)(nil),
		ExtensionType: (*bool)(nil),
		Field:         50001,
		Name:          "intellicode.api.v1.sse_enabled",
		Tag:           "varint,50001,opt,name=sse_enabled",
		Filename:      "pkg/proto/api/api.proto",
	},
}

// Extension fields to descriptorpb.MethodOptions.
var (
	// optional bool sse_enabled = 50001;
	E_SseEnabled = &file_pkg_proto_api_api_proto_extTypes[0]
)

var File_pkg_proto_api_api_proto protoreflect.FileDescriptor

var file_pkg_proto_api_api_proto_rawDesc = []byte{
	0x0a, 0x17, 0x70, 0x6b, 0x67, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x12, 0x69, 0x6e, 0x74, 0x65, 0x6c,
	0x6c, 0x69, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x1a, 0x20, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x70,
	0x6b, 0x67, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x2f, 0x65,
	0x76, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x7a, 0x0a, 0x0d, 0x52, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x72,
	0x65, 0x70, 0x6f, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x72, 0x65, 0x70, 0x6f, 0x50, 0x61, 0x74, 0x68, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b,
	0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x12, 0x29, 0x0a, 0x03, 0x69,
	0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x6c,
	0x6c, 0x69, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x44,
	0x45, 0x52, 0x03, 0x69, 0x64, 0x65, 0x2a, 0x3d, 0x0a, 0x03, 0x49, 0x44, 0x45, 0x12, 0x13, 0x0a,
	0x0f, 0x49, 0x44, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d, 0x49, 0x44, 0x45, 0x5f, 0x4a, 0x45, 0x54, 0x42, 0x52, 0x41,
	0x49, 0x4e, 0x53, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x49, 0x44, 0x45, 0x5f, 0x56, 0x53, 0x43,
	0x4f, 0x44, 0x45, 0x10, 0x02, 0x32, 0x78, 0x0a, 0x0d, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x67, 0x0a, 0x0c, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d,
	0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x12, 0x21, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x6c, 0x6c, 0x69,
	0x63, 0x6f, 0x64, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x69, 0x6e, 0x74, 0x65,
	0x6c, 0x6c, 0x69, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x22, 0x17, 0x88, 0xb5, 0x18, 0x01, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x0d, 0x22, 0x0b, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x3a,
	0x41, 0x0a, 0x0b, 0x73, 0x73, 0x65, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x1e,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xd1,
	0x86, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x73, 0x73, 0x65, 0x45, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x64, 0x42, 0x1b, 0x5a, 0x19, 0x69, 0x6e, 0x74, 0x65, 0x6c, 0x6c, 0x69, 0x63, 0x6f, 0x64,
	0x65, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pkg_proto_api_api_proto_rawDescOnce sync.Once
	file_pkg_proto_api_api_proto_rawDescData = file_pkg_proto_api_api_proto_rawDesc
)

func file_pkg_proto_api_api_proto_rawDescGZIP() []byte {
	file_pkg_proto_api_api_proto_rawDescOnce.Do(func() {
		file_pkg_proto_api_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_pkg_proto_api_api_proto_rawDescData)
	})
	return file_pkg_proto_api_api_proto_rawDescData
}

var file_pkg_proto_api_api_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_pkg_proto_api_api_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pkg_proto_api_api_proto_goTypes = []interface{}{
	(IDE)(0),                           // 0: intellicode.api.v1.IDE
	(*ReviewRequest)(nil),              // 1: intellicode.api.v1.ReviewRequest
	(*descriptorpb.MethodOptions)(nil), // 2: google.protobuf.MethodOptions
	(*event.Event)(nil),                // 3: intellicode.event.v1.Event
}
var file_pkg_proto_api_api_proto_depIdxs = []int32{
	0, // 0: intellicode.api.v1.ReviewRequest.ide:type_name -> intellicode.api.v1.IDE
	2, // 1: intellicode.api.v1.sse_enabled:extendee -> google.protobuf.MethodOptions
	1, // 2: intellicode.api.v1.ReviewService.StreamReview:input_type -> intellicode.api.v1.ReviewRequest
	3, // 3: intellicode.api.v1.ReviewService.StreamReview:output_type -> intellicode.event.v1.Event
	3, // [3:4] is the sub-list for method output_type
	2, // [2:3] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	1, // [1:2] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_pkg_proto_api_api_proto_init() }
func file_pkg_proto_api_api_proto_init() {
	if File_pkg_proto_api_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pkg_proto_api_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReviewRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pkg_proto_api_api_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 1,
			NumServices:   1,
		},
		GoTypes:           file_pkg_proto_api_api_proto_goTypes,
		DependencyIndexes: file_pkg_proto_api_api_proto_depIdxs,
		EnumInfos:         file_pkg_proto_api_api_proto_enumTypes,
		MessageInfos:      file_pkg_proto_api_api_proto_msgTypes,
		ExtensionInfos:    file_pkg_proto_api_api_proto_extTypes,
	}.Build()
	File_pkg_proto_api_api_proto = out.File
	file_pkg_proto_api_api_proto_rawDesc = nil
	file_pkg_proto_api_api_proto_goTypes = nil
	file_pkg_proto_api_api_proto_depIdxs = nil
}
