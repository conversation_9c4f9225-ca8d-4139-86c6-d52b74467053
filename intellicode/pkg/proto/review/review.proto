syntax = "proto3";

package intellicode.review.v1;

import "google/protobuf/timestamp.proto";

option go_package = "intellicode/pkg/proto/review";


enum Status {
  SessionStatus_UNSPECIFIED = 0;
  UNDONE = 1;
  DONE = 2;
  CHECKING = 3;
  FAILED = 4;
}

message Record {
  int64 id = 1;
  string repo_url = 2;
  string review_md5 = 3;
  repeated string files = 4;
  Status status = 5;
  int64 trigger_id = 6;
  string trigger_by = 7;
  string email = 8;
  google.protobuf.Timestamp created_at = 9;
}


enum Feedback {
  FeedbackAction_UNSPECIFIED = 0;
  APPLY = 1;
  REJECT = 2;
}


message FeedbackReq {
  int64 suggestion_id = 1;
  Feedback action = 2;
}
