# Go parameters
GOCMD=go
GOTEST=$(GOCMD) test
GOMOD=$(GOCMD) mod

# Project parameters
PROJECT_NAME=intellicode
TEST_PACKAGES=./...

# Build parameters
BUILD_DIR=build
SCRIPTS_DIR=scripts
BUILD_SCRIPT=$(SCRIPTS_DIR)/build.sh

# Coverage parameters
COVERAGE_DIR=test/coverage
COVERAGE_FILE=$(COVERAGE_DIR)/coverage.out
COVERAGE_HTML=$(COVERAGE_DIR)/coverage.html
COVERAGE_XML=$(COVERAGE_DIR)/coverage.xml

# Default target
.PHONY: all
all: help

# =============================================================================
# Unit Testing Commands
# =============================================================================

# Run unit tests
.PHONY: unit-test
unit-test:
	@echo "Running unit tests..."
	$(GOTEST) -v $(TEST_PACKAGES)

# Run unit tests with coverage
.PHONY: unit-test-coverage
unit-test-coverage:
	@echo "Running unit tests with coverage..."
	@mkdir -p $(COVERAGE_DIR)
	$(GOTEST) -v -coverprofile=$(COVERAGE_FILE) $(TEST_PACKAGES)
	@echo "Coverage report generated: $(COVERAGE_FILE)"

# Generate HTML coverage report
.PHONY: unit-test-coverage-html
unit-test-coverage-html: unit-test-coverage
	@echo "Generating HTML coverage report..."
	$(GOCMD) tool cover -html=$(COVERAGE_FILE) -o $(COVERAGE_HTML)
	@echo "HTML coverage report generated: $(COVERAGE_HTML)"
	@echo "Open $(COVERAGE_HTML) in your browser to view the report"

# Generate XML coverage report (for CI/CD)
.PHONY: unit-test-coverage-xml
unit-test-coverage-xml: unit-test-coverage
	@echo "Generating XML coverage report..."
	@command -v gocov >/dev/null 2>&1 || { echo "Installing gocov..."; go install github.com/axw/gocov/gocov@latest; }
	@command -v gocov-xml >/dev/null 2>&1 || { echo "Installing gocov-xml..."; go install github.com/AlekSi/gocov-xml@latest; }
	gocov convert $(COVERAGE_FILE) | gocov-xml > $(COVERAGE_XML)
	@echo "XML coverage report generated: $(COVERAGE_XML)"

# Show coverage in terminal
.PHONY: unit-test-coverage-func
unit-test-coverage-func: unit-test-coverage
	@echo "Coverage by function:"
	$(GOCMD) tool cover -func=$(COVERAGE_FILE)

# Run unit tests with race detection
.PHONY: unit-test-race
unit-test-race:
	@echo "Running unit tests with race detection..."
	$(GOTEST) -v -race $(TEST_PACKAGES)

# Run unit test benchmarks
.PHONY: unit-test-bench
unit-test-bench:
	@echo "Running unit test benchmarks..."
	$(GOTEST) -v -bench=. $(TEST_PACKAGES)

# Clean unit test artifacts
.PHONY: unit-test-clean
unit-test-clean:
	@echo "Cleaning unit test artifacts..."
	rm -rf $(COVERAGE_DIR)

# =============================================================================
# Future Extension Points (预留扩展空间)
# =============================================================================

# Build for current platform
.PHONY: build-plugincli
build-plugincli:
	@chmod +x $(BUILD_SCRIPT)
	$(BUILD_SCRIPT) current plugincli

# Build for specific platform and architecture
.PHONY: build-plugincli-platform
build-plugincli-platform:
	@if [ -z "$(GOOS)" ] || [ -z "$(GOARCH)" ]; then \
		echo "Error: GOOS and GOARCH must be specified"; \
		echo "Usage: make build-plugincli-platform GOOS=linux GOARCH=amd64"; \
		exit 1; \
	fi
	@chmod +x $(BUILD_SCRIPT)
	$(BUILD_SCRIPT) platform plugincli $(GOOS) $(GOARCH)

# Build for all platforms
.PHONY: build-plugincli-all
build-plugincli-all:
	@chmod +x $(BUILD_SCRIPT)
	$(BUILD_SCRIPT) all plugincli

# Build for Linux platforms
.PHONY: build-plugincli-linux
build-plugincli-linux:
	@chmod +x $(BUILD_SCRIPT)
	$(BUILD_SCRIPT) linux plugincli

# Build for macOS platforms
.PHONY: build-plugincli-darwin
build-plugincli-darwin:
	@chmod +x $(BUILD_SCRIPT)
	$(BUILD_SCRIPT) darwin plugincli

# Build for Windows platforms
.PHONY: build-plugincli-windows
build-plugincli-windows:
	@chmod +x $(BUILD_SCRIPT)
	$(BUILD_SCRIPT) windows plugincli

# Build TUI for current platform
.PHONY: build-tui
build-tui:
	@chmod +x $(BUILD_SCRIPT)
	$(BUILD_SCRIPT) current tui

# Build TUI for specific platform and architecture
.PHONY: build-tui-platform
build-tui-platform:
	@if [ -z "$(GOOS)" ] || [ -z "$(GOARCH)" ]; then \
		echo "Error: GOOS and GOARCH must be specified"; \
		echo "Usage: make build-tui-platform GOOS=linux GOARCH=amd64"; \
		exit 1; \
	fi
	@chmod +x $(BUILD_SCRIPT)
	$(BUILD_SCRIPT) platform tui $(GOOS) $(GOARCH)

# Build TUI for all platforms
.PHONY: build-tui-all
build-tui-all:
	@chmod +x $(BUILD_SCRIPT)
	$(BUILD_SCRIPT) all tui

# Build TUI for Linux platforms
.PHONY: build-tui-linux
build-tui-linux:
	@chmod +x $(BUILD_SCRIPT)
	$(BUILD_SCRIPT) linux tui

# Build TUI for macOS platforms
.PHONY: build-tui-darwin
build-tui-darwin:
	@chmod +x $(BUILD_SCRIPT)
	$(BUILD_SCRIPT) darwin tui

# Build TUI for Windows platforms
.PHONY: build-tui-windows
build-tui-windows:
	@chmod +x $(BUILD_SCRIPT)
	$(BUILD_SCRIPT) windows tui

# Build all applications for current platform
.PHONY: build
build:
	@chmod +x $(BUILD_SCRIPT)
	$(BUILD_SCRIPT) current

# Build all applications for all platforms
.PHONY: build-all
build-all:
	@chmod +x $(BUILD_SCRIPT)
	$(BUILD_SCRIPT) all

# Clean build artifacts
.PHONY: build-clean
build-clean:
	@chmod +x $(BUILD_SCRIPT)
	$(BUILD_SCRIPT) clean

# =============================================================================
# Protobuf Generation Commands
# =============================================================================

# Generate protobuf Go code
.PHONY: proto-gen
proto-gen:
	@echo "Generating protobuf Go code..."
	protoc --proto_path=. --proto_path=third_party --go_out=. --go_opt=paths=source_relative pkg/proto/event/event.proto
	protoc --proto_path=. --proto_path=third_party --go_out=. --go_opt=paths=source_relative pkg/proto/api/api.proto
	@echo "Protobuf Go code generated successfully!"

# Clean generated protobuf files
.PHONY: proto-clean
proto-clean:
	@echo "Cleaning generated protobuf files..."
	find pkg/proto -name "*.pb.go" -delete
	@echo "Generated protobuf files cleaned!"

# TODO: Code quality commands will be added here  
# Examples: fmt, lint, quality

# TODO: Dependency management commands will be added here
# Examples: deps, deps-verify, deps-update

# TODO: Docker commands will be added here
# Examples: docker-build, docker-run

# TODO: CI/CD commands will be added here
# Examples: ci, deploy

# =============================================================================
# Utility Commands
# =============================================================================

# Clean all artifacts
.PHONY: clean
clean: unit-test-clean build-clean proto-clean
	@echo "All artifacts cleaned!"

# Help target
.PHONY: help
help:
	@echo "$(PROJECT_NAME) Makefile Commands"
	@echo ""
	@echo "Build:"
	@echo "  build                     - Build all applications for current platform"
	@echo "  build-all                 - Build all applications for all platforms"
	@echo "  build-plugincli           - Build plugincli for current platform"
	@echo "  build-plugincli-platform  - Build plugincli for specific platform (GOOS=os GOARCH=arch)"
	@echo "  build-plugincli-all       - Build plugincli for all platforms"
	@echo "  build-plugincli-linux     - Build plugincli for Linux platforms"
	@echo "  build-plugincli-darwin    - Build plugincli for macOS platforms"
	@echo "  build-plugincli-windows   - Build plugincli for Windows platforms"
	@echo "  build-tui                 - Build TUI for current platform"
	@echo "  build-tui-platform        - Build TUI for specific platform (GOOS=os GOARCH=arch)"
	@echo "  build-tui-all             - Build TUI for all platforms"
	@echo "  build-tui-linux           - Build TUI for Linux platforms"
	@echo "  build-tui-darwin          - Build TUI for macOS platforms"
	@echo "  build-tui-windows         - Build TUI for Windows platforms"
	@echo "  build-clean               - Clean build artifacts"
	@echo ""
	@echo "For detailed build options, run: ./scripts/build.sh help"
	@echo ""
	@echo "Unit Testing:"
	@echo "  unit-test                 - Run unit tests"
	@echo "  unit-test-coverage        - Run unit tests with coverage"
	@echo "  unit-test-coverage-html   - Generate HTML coverage report"
	@echo "  unit-test-coverage-xml    - Generate XML coverage report"
	@echo "  unit-test-coverage-func   - Show coverage by function"
	@echo "  unit-test-race            - Run unit tests with race detection"
	@echo "  unit-test-bench           - Run unit test benchmarks"
	@echo "  unit-test-clean           - Clean unit test artifacts"
	@echo ""
	@echo "Protobuf:"
	@echo "  proto-gen                 - Generate protobuf Go code"
	@echo "  proto-clean               - Clean generated protobuf files"
	@echo ""
	@echo "Utility:"
	@echo "  clean                     - Clean all artifacts"
	@echo "  help                      - Show this help message"
	@echo ""
	@echo "Future extensions will be added in separate sections above."