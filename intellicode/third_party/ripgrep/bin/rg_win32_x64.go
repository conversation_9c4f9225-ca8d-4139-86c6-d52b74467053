//go:build windows && amd64
// +build windows,amd64

package bin

import (
	"os"

	"embed"

	"github.com/pkg/errors"
)

//go:embed rg_win32_x64.exe
var f embed.FS
var binName = "rg_win32_x64.exe"

func CopyToBinPath(dst string) error {
	file, err := f.ReadFile(binName)
	if err != nil {
		return errors.Wrap(err, "failed to read embedded ripgrep binary")
	}
	if err = os.WriteFile(dst, file, 0700); err != nil {
		return errors.Wrap(err, "failed to write ripgrep binary to destination")
	}
	return nil
}
