//go:build darwin && arm64
// +build darwin,arm64

package bin

import (
	"embed"
	"github.com/pkg/errors"
	"os"
)

//go:embed rg_darwin_arm64
var f embed.FS

var binName = "rg_darwin_arm64"

func CopyToBinPath(dst string) error {
	file, err := f.ReadFile(binName)
	if err != nil {
		return errors.Wrap(err, "failed to read embedded ripgrep binary")
	}
	if err = os.WriteFile(dst, file, 0700); err != nil {
		return errors.Wrap(err, "failed to write ripgrep binary to destination")
	}
	return nil
}