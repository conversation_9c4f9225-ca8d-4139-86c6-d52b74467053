# TUI UI 布局和组件设计

## 1. 总体布局

TUI 采用垂直分栏布局，将屏幕划分为三个主要区域：主输出视图、建议视图和页脚。这种布局旨在在有限的终端空间内清晰地组织信息，确保核心内容（输出和建议）始终可见，同时最大化可用空间。

```
+------------------------------------------------------+
|                                                      |
|                                                      |
| Main Output View (Scrollable, Real-time Updates)     |
|                                                      |
|                                                      |
|                                                      |
+------------------------------------------------------+
| Suggestions View (Fixed List, Interactive)           |
+------------------------------------------------------+
| Footer (Status, Keybindings, Help)                   |
+------------------------------------------------------+
```

## 2. 组件详解

### 2.1. 主输出视图 (Main Output View)

- **目的**: 按时间顺序流式展示工具执行和 LLM 响应的完整历史记录。
- **组件**: `bubbles/viewport`
- **内容**:
  - **`toolstart` 事件**: 显示工具名称和输入。例如: `> ReadMultipleFiles: [file1.go, file2.go]`。
  - **`toolend` 事件**: 显示工具执行结果（成功/失败）和简化的输出。例如: `✓ ReadMultipleFiles: Read 2 files, total 150 lines`。
  - **`assistant` 事件**: 实时追加 LLM 生成的 Markdown 内容。
- **特性**:
  - **自动滚动**: 新内容出现时自动滚动到底部。
  - **手动滚动**: 用户可以使用 `↑/↓` 或 `j/k` 键自由滚动查看历史记录。
  - **焦点样式**: 当此视图获得焦点时，边框会高亮显示。

### 2.2. 建议视图 (Suggestions View)

- **目的**: 集中展示所有可操作的修复建议，并允许用户进行交互。
- **组件**: `bubbles/list`
- **内容**:
  - 列表中的每一项代表一个 `suggestion`。
  - **默认视图 (折叠)**:
    - `[ ] [Major] [code_defect] src/main.go:15-20 潜在的空指针引用`
    - `[✔] [Minor] [refactor] src/utils.go:45-50 简化复杂的条件表达式`
  - **展开视图**:
    - 当用户选中一个建议并按 `Enter` 时，会展开显示 `old_string` 和 `new_string` 的 diff 视图。
- **特性**:
  - **固定位置**: 该视图高度固定，始终显示在屏幕底部，不会被新输出推走。
  - **状态图标**:
    - `[ ]`: 待处理 (Pending)
    - `[✔]`: 已应用 (Applied)
    - `[✖]`: 已拒绝 (Rejected)
  - **交互**: 用户可以使用 `↑/↓` 或 `j/k` 导航，使用 `Enter` 展开/折叠，使用 `a` 应用，`d` 拒绝。
  - **焦点样式**: 当此视图获得焦点时，整个列表或选中项会高亮。

### 2.3. 页脚 (Footer)

- **目的**: 提供审查状态、关键的快捷键提示和上下文信息。
- **组件**: `lipgloss.JoinHorizontal`
- **内容**:
  - **左侧**: 审查状态，例如 `[● REVIEWING]`, `[✔ COMPLETED]`, `[✖ FAILED]`。使用图标和颜色来增强可读性。
  - **中间**: 当前焦点所在的视图，例如 `[Output]` 或 `[Suggestions]`。
  - **右侧**: 核心快捷键提示，例如 `Tab: Switch Focus | q: Quit | ?: Help`。
- **样式**:
  - 通常使用反色或不同于其他区域的背景色，以示区分。
  - 状态信息使用粗体和颜色突出显示。

## 3. 样式和主题 (Lipgloss)

- **色彩**: 使用 `lipgloss` 定义一套完整的主题，包括前景、背景、边框和高亮颜色。主题应考虑不同终端配色方案的兼容性。
- **布局**: 使用 `lipgloss` 的 `Place`, `JoinVertical`, `JoinHorizontal` 等功能实现响应式布局，确保在不同尺寸的终端下都能良好显示。
- **图标**: 使用 Nerd Fonts 或简单的 Unicode 符号（如 `✓`, `✖`, `●`）来提供视觉提示，并确保有回退方案。
