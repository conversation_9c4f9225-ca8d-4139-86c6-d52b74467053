# TUI 技术实现指南

## 1. 项目初始化

1. **创建项目结构**: 遵循标准的 Go 项目布局，我们将核心 TUI 逻辑放在 `internal/tui`，而 `cmd/tui` 仅作为程序入口。

    ```
    ├── cmd/
    │   └── tui/
    │       └── main.go      # 程序入口，负责初始化和启动 TUI 应用
    └── internal/
        └── tui/             # TUI 核心逻辑包
            ├── app.go       # 应用启动器 (e.g., NewApp, Run)
            ├── model.go     # 主模型 (TUIModel) 和 Update/View 逻辑
            ├── sse.go       # SSE 客户端实现
            ├── components.go# UI 组件 (viewport, list, etc.)
            └── msgs.go      # 自定义 tea.Msg 定义
    ```

2. **添加依赖**: 使用 `go get` 添加 Charm.sh 相关的依赖。

    ```bash
    go get github.com/charmbracelet/bubbletea
    go get github.com/charmbracelet/bubbles
    go get github.com/charmbracelet/lipgloss
    ```

3. **实现 `main.go`**: `cmd/tui/main.go` 的职责非常简单：

    ```go
    package main

    import (
        "fmt"
        "os"
        "project/internal/tui" // 替换为实际的项目路径
    )

    func main() {
        app, err := tui.NewApp() // NewApp 在 internal/tui/app.go 中定义
        if err != nil {
            fmt.Println("Error initializing TUI:", err)
            os.Exit(1)
        }

        if err := app.Run(); err != nil {
            fmt.Println("Error running TUI:", err)
            os.Exit(1)
        }
    }
    ```
