# TUI UX 交互设计

## 1. 核心交互流程

IntelliCode TUI 的核心交互围绕“观察”和“行动”两个阶段展开。用户首先观察实时代码审查的进展，然后在需要时对生成的建议采取行动。

```mermaid
graph TD
    A[启动 TUI] --> B{发起审查请求};
    B --> C[观察实时输出流];
    C --> D{出现修复建议};
    D --> E[切换焦点到建议列表];
    E --> F{选择并操作建议};
    F --> G[应用/拒绝建议];
    G --> H[观察建议状态变化];
    H --> E; 
    C --> I[审查完成];
    I --> J[退出应用];
```

## 2. 键盘快捷键

键盘是 TUI 的主要输入方式。快捷键设计遵循直观、高效和一致的原则。

### 2.1. 全局快捷键

| 按键 | 功能 |
|---|---|
| `q` / `Ctrl+c` | 退出应用 |
| `Tab` | 在主输出视图和建议视图之间切换焦点 |
| `?` | 显示/隐藏帮助弹窗 |

### 2.2. 主输出视图 (焦点)

| 按键 | 功能 |
|---|---|
| `↑` / `k` | 向上滚动一行 |
| `↓` / `j` | 向下滚动一行 |
| `PageUp` | 向上翻一页 |
| `PageDown` | 向下翻一页 |
| `g` | 跳转到顶部 |
| `G` | 跳转到底部 |

### 2.3. 建议视图 (焦点)

| 按键 | 功能 |
|---|---|
| `↑` / `k` | 选择上一个建议 |
| `↓` / `j` | 选择下一个建议 |
| `Enter` | 展开/折叠选中的建议，显示 Diff 视图 |
| `a` | 应用选中的建议 |
| `d` | 拒绝选中的建议 |
| `o` | 在默认编辑器中打开建议所在的文件 |

## 3. 交互反馈

及时的反馈是良好用户体验的关键。

- **焦点切换**: 当用户按 `Tab` 键时，新获得焦点的视图（主输出或建议）的边框会高亮，页脚会明确指示当前焦点，例如 `[Focus: Suggestions]`。
- **建议操作**:
  - **应用 (`a`)**: 建议项前的图标立即变为 `[✔]`，并显示一条临时状态消息，如 `Applied suggestion #3`。
  - **拒绝 (`d`)**: 图标变为 `[✖]`，并显示消息 `Rejected suggestion #3`。
- **状态变化**: 页眉的审查状态会实时更新，并伴有颜色和图标的变化，清晰地传达当前进展（如 `REVIEWING` -> `COMPLETED`）。
- **错误处理**: 当发生连接错误或内部错误时，会在页脚或一个临时的弹窗中显示清晰、无技术术语的错误信息，并提供重试选项（如果适用）。

## 4. 特殊交互场景

### 4.1. 帮助弹窗

- 当用户按 `?` 时，一个居中的弹窗会覆盖在当前界面之上，显示所有可用的快捷键。
- 再次按 `?` 或 `Esc` 可以关闭帮助弹窗。

### 4.2. Diff 视图

- 在建议列表中按 `Enter` 展开建议时，会显示一个彩色的、并排或内联的 Diff 视图，清晰地展示 `old_string` 和 `new_string` 的区别。
- Diff 视图会使用红色和绿色来高亮删除和添加的行。

### 4.3. 响应式调整

- 当终端窗口大小改变时，UI 布局会自动调整。
- 在非常窄的屏幕上，建议视图可能会减少显示的信息量（例如，只显示描述），以保证可读性。
