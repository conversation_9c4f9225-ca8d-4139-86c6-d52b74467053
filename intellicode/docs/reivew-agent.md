# review agent 迭代任务记录

## 改造

### intellicode 进程启动和初始化

- [ ] nhctl intellicode init、start 子命令的重构改造
- [ ] plugin client 对 intellicode 进程的启动、探活处理逻辑（涉及的弹窗通知）

## Agent 数据

- ReviewAgent 需要返回 session id 对应着 db 的 ReviewRecord，对应 langfuse 的 Session id
- ReviewAgent 需要创建 ~/.nh/intellicode/projects/xx 的目录
- ReviewAgent 需要按照 session 在 project 目录中持久化 event 记录(需要 tool、assistant 的 event 记录)
- ReviewAgent 需要通过 pragent 实现 Suggestion 数据管理功能
- ReviewAgent 不返回 Suggestion，统一使用 event 处理 Suggestion 数据，与 pragent 协同创建好 Suggestion 并且持久化
  - Suggestion to ReviewIssue 数据转化
- ReviewAgent session 管理功能
  - session list api
  - session event list api
  - session suggestion list api

### ReviewAgent Service

暴露 ReviewAgent Service 对外，通过 protobuf 定义接口。在 pkg 下面实现。
最终 nhclt http server 集成 ReviewAgent Service。

## pragent

API 设计：

- CreateReview Record —— Review 开始时，由 Agent 返回的基础数据。
- Create Issues —— 通过 Suggestion Event 实时创建 ReviewIssueRecord
- Create Issue Feedback —— 用户针对 Suggestion 的 Feedback

## Nhctl

- 移除原本 intellicode 的 Embed 和相关的子命令代码
- damon server 集成 http handler
