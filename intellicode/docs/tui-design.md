# IntelliCode TUI 设计文档

## 概述

IntelliCode TUI (Terminal User Interface) 是基于 Charm.sh 生态系统构建的终端用户界面，旨在提供类似 Claude Code 的流式交互体验。TUI 通过 HTTP SSE 与后端 Agent 系统通信，实时展示代码审查过程和结果。

## 核心设计理念

### 1. 持续输出体验
- **流式显示**: 参考 Claude Code 的输出方式，工具调用和 LLM 响应按时间顺序持续追加
- **实时反馈**: 用户可以实时看到代码审查的进展过程
- **历史记录**: 保留完整的执行历史，支持回溯查看

### 2. 交互式建议管理
- **固定展示**: 修复建议固定在屏幕底部，不会被新内容推走
- **动态更新**: 随着 suggestions 事件持续更新建议列表
- **直接操作**: 支持直接应用或拒绝建议，提供即时反馈

### 3. 用户体验优先
- **响应式设计**: 适配不同终端尺寸
- **键盘友好**: 完整的键盘快捷键支持
- **视觉清晰**: 使用色彩和图标增强信息层次

## 目录结构

```
docs/
├── tui-design.md              # 本文档 - 总体设计概述
├── tui-architecture.md        # 技术架构设计
├── tui-ui-layout.md          # UI 布局和组件设计
├── tui-ux-interaction.md     # UX 交互设计
├── tui-event-flow.md         # 事件流和数据模型
└── tui-implementation.md     # 技术实现指南
```

## 关键特性

### 技术特性
- **基于 Charm.sh**: 使用 Bubbletea、Bubbles、Lipgloss 构建
- **HTTP SSE 通信**: 与后端 Agent 系统通过 SSE 实时通信
- **事件驱动**: 基于 protocol.md 定义的事件协议
- **跨平台**: 支持 macOS、Linux、Windows 终端

### 用户特性
- **实时展示**: 工具执行状态、LLM 响应、修复建议的实时更新
- **交互操作**: 建议的展开/折叠、应用/拒绝
- **历史查看**: 完整的执行历史记录
- **快捷操作**: 丰富的键盘快捷键支持

## 设计原则

### 1. 一致性
- 与现有 CLI 输出风格保持一致
- 遵循 Charm.sh 生态系统的设计规范
- 统一的色彩和图标体系

### 2. 可用性
- 清晰的信息层次结构
- 直观的交互反馈
- 完善的错误处理和提示

### 3. 性能
- 高效的事件处理
- 合理的内存管理
- 流畅的用户体验

### 4. 可扩展性
- 模块化的组件设计
- 可配置的主题和布局
- 易于添加新功能

## 相关文档

- [实时通信协议设计](protocol.md) - 事件协议定义
- [项目架构文档](CLAUDE.md) - 整体项目架构
- [Charm.sh 官方文档](https://charm.sh) - 技术栈参考

---

*本文档是 IntelliCode TUI 设计的总体概述，详细内容请参考各个子文档。*