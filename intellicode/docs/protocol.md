# IntelliCode 实时通信协议设计

## 概述

基于 HTTP + SSE 的轻量级实时通信协议，支持代码审查过程的实时展示和交互。

核心设计原则：

- **HTTP 请求即会话**：一个 HTTP 请求对应一个 Agent 生命周期
- **SSE 事件流**：实时推送工具执行状态、LLM 响应和修复建议
- **轻量级架构**：最小化组件设计，避免过度抽象

## UI 展示需求

### Tool 执行状态展示

- **TodoWrite**: 展示 input，状态始终为 success
- **TodoRead**: 无需展示
- **Read/ReadMultipleFiles**: 展示 input，output 精简显示文件行数统计
- **RipGrep**: 展示 input，output 精简显示匹配结果数量
- **LsTree**: 展示 input，output 精简显示文件/目录数量
- **IssueAnalyzer/IssueRecord/IssueFixer**: 仅展示 input，状态为 success

### 实时内容展示

- **LLM 流式响应**: 实时追加 Markdown 内容
- **修复建议**: 追加模式展示 Suggestion 列表
- **工具状态**: 实时更新工具执行进度

## SSE 事件协议

### 事件类型

| 事件类型 | 用途 | 触发时机 |
|---------|------|----------|
| `toolstart` | 工具开始执行 | Tool Callback OnStart |
| `toolend` | 工具执行完成 | Tool Callback OnEnd |
| `assistant` | LLM 流式响应 | LLM 实时输出 |
| `suggestions` | 修复建议 | IssueRecord/IssueFixer 完成 |

### 工具状态枚举

工具状态使用 ToolStatus 枚举值：

| 状态值 | 描述 |
|--------|------|
| `TOOL_STATUS_UNSPECIFIED` | 未指定状态 |
| `TOOL_STATUS_RUNNING` | 工具正在运行 |
| `TOOL_STATUS_SUCCESS` | 工具执行成功 |
| `TOOL_STATUS_ERROR` | 工具执行出错 |

### 事件数据格式

#### 工具执行事件

**toolstart 事件**：

```
event: toolstart
data: {
  "name": "ReadMultipleFiles",
  "input": "files: [src/main.go, src/utils.go]",
  "status": "TOOL_STATUS_RUNNING"
}
```

**toolend 事件**：

```
event: toolend
data: {
  "name": "ReadMultipleFiles", 
  "input": "files: [src/main.go, src/utils.go]",
  "output": "Read 2 files, total 150 lines",
  "status": "TOOL_STATUS_SUCCESS"
}
```

#### LLM 响应事件

**assistant 事件**：

```
event: assistant
data: {
  "chunk": "## 代码审查报告\n\n发现以下问题..."
}
```

#### 修复建议事件

**suggestions 事件**：

```
event: suggestions
data: {
  "suggestions": [{
    "id": 1,
    "category": "code_defect",
    "level": "Major",
    "file_path": "src/main.go",
    "start_line": 15,
    "end_line": 20,
    "description": "潜在的空指针引用",
    "old_string": "if user != nil {\n  return user.Name\n}",
    "new_string": "if user == nil {\n  return \"\"\n}\nreturn user.Name"
  }]
}
```

## HTTP API 设计

### 代码审查接口

**POST /api/review**

请求体：

```json
{
  "repo_path": "/path/to/repository"
  "review_files": ["repo/a.py", "repo/b/foo.py"],
}
```

响应：

- Content-Type: `text/event-stream`
- 实时返回 SSE 事件流
- Agent 执行完成后自动结束流

### 使用流程

```javascript
// 前端调用示例
const response = await fetch('/api/review', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ repo_path: '/path/to/repo' })
});

// 处理 SSE 事件流
const reader = response.body.getReader();
const decoder = new TextDecoder();

while (true) {
  const { done, value } = await reader.read();
  if (done) break;
  
  const chunk = decoder.decode(value);
  // 解析 SSE 事件并更新 UI
  handleSSEChunk(chunk);
}
```

## 技术架构

### 核心组件

#### EventEmitter

```go
type Emitter struct {
    eventChan chan Event  // 单一事件通道
}

// 便利方法
func (e *Emitter) EmitToolStart(name, input string)
func (e *Emitter) EmitToolEnd(name, input, output, status string)  
func (e *Emitter) EmitAssistant(chunk string)
func (e *Emitter) EmitSuggestions(suggestions []Suggestion)
```

#### Tool 重构架构

**统一 ToolWrapper 接口**：

```go
type ToolWrapper interface {
    tool.InvokableTool
    SetEventEmitter(emitter EventEmitter)
    FormatEvent(input, result string, err error) string
    ShouldShowInUI() bool
    GetName() string
}
```

**可选 BaseTool 辅助**：

```go
type BaseTool struct {
    name     string
    desc     string
    schema   *openapi3.Schema
    emitter  EventEmitter
}

// 提供统一的事件发送、Info 方法等基础功能
```

**独立工具实现**：

- **IssueRecordTool**: 内部维护 `issues []IssueRecordItem`
- **IssueFixTool**: 内部维护 `suggestions []Suggestion`
- **ReadTool**: 无状态工具，专注文件操作
- **其他工具**: 各自维护相应的业务数据

**统一事件处理**：

```go
func (tool *AnyTool) InvokableRun(ctx, input, opts) (string, error) {
    // 1. 发送 toolstart 事件
    tool.EmitToolStart(ctx, input)
    
    // 2. 执行业务逻辑
    result, err := tool.process(ctx, input)
    
    // 3. 发送 toolend 事件
    tool.EmitToolEnd(ctx, input, tool.FormatEvent(input, result, err), status)
    
    return result, err
}
```

#### Agent 集成

- **依赖注入**: Agent 构造时可选注入 EventEmitter
- **工具创建**: 统一创建所有 ToolWrapper 实例并注入 EventEmitter
- **CLI 兼容**: 默认使用 NopEmitter，保持现有功能不变

#### HTTP Handler

```go
func handleReviewWithSSE(w http.ResponseWriter, r *http.Request) {
    // 1. 设置 SSE 响应头
    // 2. 创建 Emitter 
    // 3. 创建 Agent 并注入 Emitter
    // 4. 异步执行 Agent
    // 5. 监听事件通道并推送给客户端
}
```

### Tool 重构设计原则

#### 数据独立性

- 每个工具维护自己的业务数据，避免全局状态
- 工具间通过明确的接口进行数据交换
- 支持工具内部的并发安全控制

#### 接口统一性

- 所有工具实现相同的 ToolWrapper 接口
- 统一的事件发送机制，无需每个工具单独处理
- 标准化的输出格式化流程

#### 可扩展性

- 新工具只需实现 ToolWrapper 接口即可
- 可选择性地使用 BaseTool 提供的辅助功能

#### 架构优势

1. **职责清晰**: 每个工具专注于自己的业务逻辑
2. **数据隔离**: 工具间数据完全独立，避免耦合
3. **事件自动化**: 统一的事件发送机制，减少重复代码
4. **类型安全**: 接口保证了工具行为的一致性
5. **测试友好**: 每个工具可以独立测试

### 生命周期管理

```
HTTP Request → Create Emitter → Create Agent → Agent.CreateTools → Execute Review → Stream Events → Request End
     ↓              ↓              ↓             ↓                ↓              ↓             ↓
  开始审查      创建事件通道      Agent初始化    工具初始化         工具执行        实时推送      自动清理
```

### 前端实现

支持多种前端形态：

- **TUI**: 使用 Charm.sh 构建终端界面
- **Web**: 标准 Web 页面 + JavaScript SSE 处理  
- **IDE 插件**: VS Code/IntelliJ 等插件形式

所有前端通过统一的 HTTP + SSE 协议与后端通信，实现完全的前后端分离。
