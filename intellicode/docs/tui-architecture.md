# IntelliCode TUI 技术架构设计

## 架构概述

IntelliCode TUI 采用事件驱动的架构模式，通过 HTTP SSE 与后端 Agent 系统通信，使用 Charm.sh 生态系统构建终端用户界面。

```
┌─────────────────┐    HTTP POST /api/review    ┌─────────────────┐
│                 │ ──────────────────────────> │                 │
│   TUI Client    │                             │  Agent Server   │
│   (Charm.sh)    │ <────────────────────────── │   (Go HTTP)     │
│                 │    SSE Event Stream         │                 │
└─────────────────┘                             └─────────────────┘
```

## 技术栈

### 核心框架

- **Bubbletea**: Elm 架构的 TUI 框架
- **Bubbles**: 预构建的 UI 组件库
- **Lipgloss**: 样式和布局系统

### 通信协议

- **HTTP Client**: 发起代码审查请求
- **SSE (Server-Sent Events)**: 接收实时事件流
- **JSON**: 事件数据序列化格式

### 依赖库

- **Go标准库**: net/http, encoding/json, context
- **第三方库**:
  - github.com/charmbracelet/bubbletea
  - github.com/charmbracelet/bubbles
  - github.com/charmbracelet/lipgloss

## 核心组件架构

### 1. SSE 客户端 (SSEClient)

```go
type SSEClient struct {
    baseURL    string
    httpClient *http.Client
    eventChan  chan SSEEvent
    done       chan struct{}
    cancel     context.CancelFunc
}

type SSEEvent struct {
    Type      string          `json:"type"`
    Data      json.RawMessage `json:"data"`
    Timestamp time.Time       `json:"timestamp"`
}
```

**职责**:

- 建立 SSE 连接
- 解析 SSE 事件流
- 事件分发到应用层
- 连接管理和错误处理

### 2. TUI 应用模型 (TUIModel)

```go
type TUIModel struct {
    // 应用状态
    width, height int
    focusedPane   FocusPane
    
    // 数据模型
    outputBuffer    *OutputBuffer
    suggestions     []SuggestionItem
    
    // UI 组件
    outputViewport    viewport.Model
    suggestionsList   list.Model
    
    // SSE 客户端
    sseClient *SSEClient
    
    // 状态管理
    isConnected bool
    isReviewing bool
    lastError   error
}
```

**职责**:

- 应用状态管理
- 事件处理和分发
- UI 组件协调
- 用户交互处理

### 3. 输出缓冲区 (OutputBuffer)

```go
type OutputBuffer struct {
    entries []OutputEntry
    maxSize int
    mutex   sync.RWMutex
}

type OutputEntry struct {
    Type      OutputType
    Content   string
    Timestamp time.Time
    Metadata  map[string]interface{}
}
```

**职责**:

- 管理输出内容
- 格式化显示内容
- 内存管理（滚动窗口）
- 线程安全操作

### 4. 建议管理器 (SuggestionManager)

```go
type SuggestionManager struct {
    suggestions []SuggestionItem
    selectedIdx int
    mutex       sync.RWMutex
}

type SuggestionItem struct {
    ID         int
    Category   string
    Level      string
    FilePath   string
    StartLine  int
    EndLine    int
    Description string
    OldString  string
    NewString  string
    Status     SuggestionStatus
    IsExpanded bool
}

type SuggestionStatus string
const (
    StatusPending  SuggestionStatus = "pending"
    StatusApplied  SuggestionStatus = "applied"
    StatusRejected SuggestionStatus = "rejected"
)
```

**职责**:

- 建议数据管理
- 状态追踪
- 用户操作处理
- 文件系统操作

## 事件处理流程

### 1. 事件接收流程

```
SSE Stream → SSEClient.parseEvent() → EventChannel → TUIModel.Update()
```

### 2. 事件类型映射

```go
func (m *TUIModel) handleSSEEvent(event SSEEvent) tea.Cmd {
    switch event.Type {
    case "toolstart":
        return m.handleToolStart(event.Data)
    case "toolend":
        return m.handleToolEnd(event.Data)
    case "assistant":
        return m.handleAssistant(event.Data)
    case "suggestions":
        return m.handleSuggestions(event.Data)
    default:
        return m.handleUnknownEvent(event)
    }
}
```

### 3. 消息生成流程

```go
// 事件 → Bubbletea 消息
type ToolStartMsg struct {
    Name   string
    Input  string
    Status string
}

type AssistantMsg struct {
    Chunk string
}

type SuggestionMsg struct {
    Suggestions []Suggestion
}
```

## 状态管理

### 1. 应用状态机

```
┌─────────────┐    StartReview    ┌─────────────┐
│    Idle     │ ────────────────> │  Reviewing  │
│             │                   │             │
└─────────────┘                   └─────────────┘
       ^                                   │
       │              ReviewComplete       │
       └───────────────────────────────────┘
```

### 2. 焦点管理

```go
type FocusPane int
const (
    FocusOutput FocusPane = iota
    FocusSuggestions
)

func (m *TUIModel) switchFocus() {
    switch m.focusedPane {
    case FocusOutput:
        m.focusedPane = FocusSuggestions
    case FocusSuggestions:
        m.focusedPane = FocusOutput
    }
}
```

### 3. 并发安全

```go
type SafeState struct {
    mu    sync.RWMutex
    data  map[string]interface{}
}

func (s *SafeState) Set(key string, value interface{}) {
    s.mu.Lock()
    defer s.mu.Unlock()
    s.data[key] = value
}

func (s *SafeState) Get(key string) interface{} {
    s.mu.RLock()
    defer s.mu.RUnlock()
    return s.data[key]
}
```

## 布局管理

### 1. 响应式布局

```go
func (m *TUIModel) calculateLayout() LayoutConfig {
    config := LayoutConfig{
        OutputHeight:     m.height - suggestionBaseHeight - headerHeight - footerHeight,
        SuggestionHeight: suggestionBaseHeight,
        Width:           m.width,
    }
    
    // 小屏幕适配
    if m.width < 80 {
        config.SuggestionHeight = min(config.SuggestionHeight, m.height/3)
    }
    
    return config
}
```

### 2. 组件组合

```go
func (m TUIModel) View() string {
    layout := m.calculateLayout()
    
    header := m.renderHeader()
    output := m.renderOutput(layout.OutputHeight)
    suggestions := m.renderSuggestions(layout.SuggestionHeight)
    footer := m.renderFooter()
    
    return lipgloss.JoinVertical(
        lipgloss.Left,
        header,
        output,
        suggestions,
        footer,
    )
}
```

## 错误处理

### 1. 错误类型

```go
type TUIError struct {
    Type    ErrorType
    Message string
    Cause   error
}

type ErrorType int
const (
    ErrorTypeConnection ErrorType = iota
    ErrorTypeSSE
    ErrorTypeUI
    ErrorTypeFileSystem
)
```

### 2. 错误恢复

```go
func (m *TUIModel) handleError(err error) tea.Cmd {
    tuiErr, ok := err.(*TUIError)
    if !ok {
        tuiErr = &TUIError{
            Type:    ErrorTypeUnknown,
            Message: err.Error(),
            Cause:   err,
        }
    }
    
    switch tuiErr.Type {
    case ErrorTypeConnection:
        return m.retryConnection()
    case ErrorTypeSSE:
        return m.reconnectSSE()
    default:
        return m.showError(tuiErr)
    }
}
```

## 性能优化

### 1. 内存管理

```go
// 输出缓冲区大小限制
const maxOutputEntries = 1000

func (ob *OutputBuffer) add(entry OutputEntry) {
    ob.mutex.Lock()
    defer ob.mutex.Unlock()
    
    ob.entries = append(ob.entries, entry)
    if len(ob.entries) > maxOutputEntries {
        // 移除旧条目
        ob.entries = ob.entries[len(ob.entries)-maxOutputEntries:]
    }
}
```

### 2. 渲染优化

```go
// 仅在必要时重新渲染
func (m *TUIModel) shouldRerender(msg tea.Msg) bool {
    switch msg.(type) {
    case tea.WindowSizeMsg:
        return true
    case AssistantMsg:
        return m.focusedPane == FocusOutput
    case SuggestionMsg:
        return m.focusedPane == FocusSuggestions
    default:
        return false
    }
}
```

### 3. 事件去重

```go
func (m *TUIModel) deduplicateEvents(events []SSEEvent) []SSEEvent {
    seen := make(map[string]bool)
    result := make([]SSEEvent, 0, len(events))
    
    for _, event := range events {
        key := event.Type + string(event.Data)
        if !seen[key] {
            seen[key] = true
            result = append(result, event)
        }
    }
    
    return result
}
```

## 部署和配置

### 2. 运行时配置

```go
type TUIConfig struct {
    ServerURL    string        `json:"server_url"`
    Timeout      time.Duration `json:"timeout"`
    MaxRetries   int           `json:"max_retries"`
    Theme        string        `json:"theme"`
    LogLevel     string        `json:"log_level"`
}
```

---

*本文档定义了 IntelliCode TUI 的技术架构，为后续的实现提供了详细的设计指导。*

