# TUI 事件流和数据模型设计

## 1. 总体数据流

IntelliCode TUI 的核心是一个单向数据流架构，与 Bubbletea 的 Elm 架构保持一致。外部事件（SSE 事件、用户输入）被转换为内部消息（`tea.Msg`），由 `Update` 函数处理，进而更新应用状态（`TUIModel`），最后由 `View` 函数将新状态渲染到终端。

```mermaid
graph TD
    subgraph External Events
        A[SSE Stream] --> B[SSEClient];
        C[User Keyboard Input] --> D[Bubbletea Runtime];
    end

    subgraph TUI Application
        B --> E{tea.Msg Generation};
        D --> E;
        E --> F[TUIModel.Update(msg)];
        F --> G[TUIModel (State)];
        G --> H[TUIModel.View()];
        H --> I[Render to Terminal];
    end

    G --> F; 
```

## 2. SSE 事件到内部消息的映射

`SSEClient` 负责监听来自服务器的 SSE 事件，并将其转换为具体的 `tea.Msg`，以便 `TUIModel` 能够以类型安全的方式处理它们。

| SSE 事件 (`protocol.md`) | 内部消息 (`tea.Msg`) | `TUIModel` 数据更新 |
|---|---|---|
| `toolstart` | `ToolStartMsg` | 在 `OutputBuffer` 中追加一条 `tool_start` 类型的条目。 |
| `toolend` | `ToolEndMsg` | 在 `OutputBuffer` 中追加一条 `tool_end` 类型的条目，包含成功/失败状态和摘要。 |
| `assistant` | `AssistantMsg` | 在 `OutputBuffer` 中追加或更新 `assistant_chunk` 条目，实现流式文本显示。 |
| `suggestions` | `SuggestionsMsg` | 更新 `SuggestionManager` 中的建议列表。`list.Model` 会收到新的项目。 |
| `(error)` | `ErrorMsg` | 更新 `TUIModel.lastError`，并在页脚或弹窗中显示错误信息。 |

### 示例：`ToolStartMsg`

```go
// SSE Event Data (JSON)
// { "name": "ReadMultipleFiles", "input": "..." }

// Corresponding tea.Msg
type ToolStartMsg struct {
    Name  string
    Input string
}

// TUIModel.Update logic
case ToolStartMsg:
    entry := OutputEntry{
        Type:    OutputTypeToolStart,
        Content: fmt.Sprintf("> %s: %s", msg.Name, msg.Input),
    }
    m.outputBuffer.Add(entry)
```

## 3. 核心数据模型

### 3.1. `TUIModel`

`TUIModel` 是应用的主状态容器，包含了所有子组件的模型和应用的全局状态。

```go
// tui-architecture.md 中已定义
type TUIModel struct {
    // ... (UI 组件模型)
    outputBuffer    *OutputBuffer
    suggestionManager *SuggestionManager
    sseClient       *SSEClient
    
    // 全局状态
    width, height   int
    focusedPane     FocusPane
    isReviewing     bool
    lastError       error
}
```

### 3.2. `OutputBuffer` 和 `OutputEntry`

`OutputBuffer` 存储了主输出视图的所有内容。每个 `OutputEntry` 代表一个独立的显示单元。

```go
type OutputType int
const (
    OutputTypeToolStart OutputType = iota
    OutputTypeToolEnd
    OutputTypeAssistant
    OutputTypeError
)

type OutputEntry struct {
    Type      OutputType
    Content   string
    Timestamp time.Time
}
```

### 3.3. `SuggestionManager` 和 `SuggestionItem`

`SuggestionManager` 负责管理所有从服务器接收到的建议。`SuggestionItem` 在 `protocol.md` 的 `Suggestion` 基础上增加了 UI 状态。

```go
// tui-architecture.md 中已定义
type SuggestionItem struct {
    // 来自协议的字段
    ID, FilePath, Description, ...

    // UI 特定状态
    Status     SuggestionStatus // Pending, Applied, Rejected
    IsExpanded bool             // 是否在 UI 中展开显示 Diff
}
```

## 4. 用户交互事件流

用户输入同样被视为事件，并转换为 `tea.Msg` 进行处理。

| 用户操作 | `tea.KeyMsg` | `TUIModel.Update` 逻辑 |
|---|---|---|
| 按 `Tab` | `tea.Key{Type: tea.KeyTab}` | 切换 `TUIModel.focusedPane` 的值，并更新 UI 组件的焦点状态。 |
| 在建议列表按 `a` | `tea.Key{Runes: []rune{'a'}}` | 1. 检查焦点是否在建议视图。 2. 获取当前选中的 `SuggestionItem`。 3. 将其 `Status` 更新为 `Applied`。 4. （可选）向服务器发送一个确认事件。 5. 更新 `list.Model` 以重新渲染该项。 |
| 调整窗口大小 | `tea.WindowSizeMsg` | 更新 `TUIModel.width` 和 `TUIModel.height`，并重新计算所有组件的布局。 |

这个事件驱动和单向数据流的设计确保了 TUI 的状态变化是可预测和易于管理的，同时也与 Charm.sh 生态系统的编程模型完全兼容。
