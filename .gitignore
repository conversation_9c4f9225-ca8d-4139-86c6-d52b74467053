# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib
*.test
*.out

# Go build artifacts
/build/
/bin/
/dist/

# Go modules
vendor/

# Environment files
.env
.env.local
.env.*.local

# Configuration files with sensitive data
*.local.yaml
*.local.yml
*.local.toml
*.local.json
config.local.*

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
/logs/
/testlog/

# Temporary files
*.tmp
*.temp
.pid

# Coverage reports
coverage.txt
coverage.html
coverage.out

# Dependency lock files (uncomment if needed)
# go.sum

# Build and release artifacts
/cmd/*/build/
/cmd/*/sync/
sync_for_test

# Project specific
/intellicode/build/
/nhctl/build/
/nocalhost-api
/nocalhost-dep
/nhctl

# Chart files
Chart.lock
